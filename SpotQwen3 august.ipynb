# Cell 1: Imports and Utilities
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import tifffile
import random
import cv2
from scipy.ndimage import distance_transform_edt
from skimage.measure import regionprops
from skimage.morphology import skeletonize
from scipy.spatial.distance import cdist
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
import albumentations as A
import os
import matplotlib.pyplot as plt
from tqdm import tqdm
from skimage.color import label2rgb
import glob as glob
# Compatibility for AMP
try:
    from torch.amp import GradScaler, autocast
except ImportError:
    from torch.cuda.amp import GradScaler, autocast

# --- Utility Functions ---
def get_optimized_transforms():
    """Optimized augmentation pipeline for grayscale microscopy images."""
    return A.Compose([
        A.HorizontalFlip(p=0.5),
        <PERSON><PERSON>ert<PERSON>lip(p=0.5),
        <PERSON><PERSON>(limit=90, p=0.7),
        <PERSON><PERSON>(blur_limit=(1, 3), sigma_limit=(0.1, 0.7), p=0.2),
        <PERSON><PERSON>(blur_limit=3, p=0.1),
        A.RandomBrightnessContrast(brightness_limit=0.15, contrast_limit=0.15, p=0.4),
        A.Normalize(mean=0.0, std=1.0, max_pixel_value=1.0, p=1.0)
    ], p=1.0)


# Cell: Corrected and Improved SkeletonAwareSpotDataset
import torch
import numpy as np
import tifffile
import random
import cv2
from torch.utils.data import Dataset
from scipy.ndimage import distance_transform_edt, gaussian_filter
from skimage.measure import regionprops
from skimage.morphology import skeletonize, erosion, square
from scipy.spatial.distance import cdist

class SkeletonAwareSpotDataset(Dataset):
    def __init__(self, image_paths, mask_paths, transform=None, patch_size=256):
        assert len(image_paths) == len(mask_paths)
        self.image_paths = image_paths
        self.mask_paths = mask_paths
        self.transform = transform
        self.patch_size = patch_size

    def _get_valid_spots(self, mask):
        """Get list of valid spot IDs (excluding background 0)."""
        valid_ids = np.unique(mask)
        return valid_ids[valid_ids > 0]

    def _extract_spot_info(self, mask, valid_ids):
        """Extract centroids and individual spot masks."""
        centroids = []
        spot_masks = []
        for spot_id in valid_ids:
            spot_mask = (mask == spot_id).astype(np.uint8)
            props = regionprops(spot_mask)
            if props:
                centroids.append(props[0].centroid)
                spot_masks.append(spot_mask)
        return centroids, spot_masks

    def _generate_semantic_mask(self, spot_masks, shape):
        """Combine individual spot masks into a single semantic mask."""
        semantic = np.zeros(shape, dtype=np.float32)
        for mask in spot_masks:
            semantic = np.maximum(semantic, mask.astype(np.float32))
        return semantic

    def _place_centroid_skeleton_point(self, skeleton_map, spot_region, centroid_y, centroid_x):
        """
        Helper to place a robust skeleton point at the centroid for small/empty skeletons.
        Ensures coordinates are within bounds.
        """
        H, W = skeleton_map.shape
        cy_int, cx_int = int(round(centroid_y)), int(round(centroid_x))
        if 0 <= cy_int < H and 0 <= cx_int < W:
            # Place a small peak, decaying with distance, similar to small spot logic
            y_min, y_max = max(0, cy_int - 1), min(H, cy_int + 2)
            x_min, x_max = max(0, cx_int - 1), min(W, cx_int + 2)
            Y, X = np.ogrid[y_min:y_max, x_min:x_max]
            dist_from_center = np.sqrt((Y - centroid_y)**2 + (X - centroid_x)**2)
            # Create a small, smooth peak
            skel_peak = np.clip(1.0 - dist_from_center / 2.0, 0, 1)
            # Blend with existing (though likely 0)
            skeleton_map[y_min:y_max, x_min:x_max] = np.maximum(
                skeleton_map[y_min:y_max, x_min:x_max], skel_peak
            )

    def generate_skeleton_aware_distance_transform(self, spot_mask):
        """
        Generate SDT, Skeleton, and Boundary maps for a single spot mask.
        Corrected to calculate internal distance to skeleton properly and handle edge cases.
        """
        if not np.any(spot_mask):
            return (np.zeros_like(spot_mask, dtype=np.float32),
                    np.zeros_like(spot_mask, dtype=np.float32),
                    np.zeros_like(spot_mask, dtype=np.float32))

        H, W = spot_mask.shape
        spot_area = np.sum(spot_mask)
        spot_region = spot_mask > 0

        # --- 1. Generate skeleton ---
        skeleton = np.zeros_like(spot_mask, dtype=np.float32)
        if spot_area <= 10:  # Small spots
            props = regionprops(spot_mask.astype(np.uint8))
            if props:
                cy, cx = props[0].centroid
                # Use EDT from centroid instead of hardcoded gradient
                Y, X = np.ogrid[:H, :W]
                dist_from_center = np.sqrt((Y - cy)**2 + (X - cx)**2)
                skeleton[spot_mask > 0] = np.clip(1.0 - dist_from_center[spot_mask > 0] / 2.0, 0, 1)
        else:  # Larger spots
            try:
                skel_temp = skeletonize(spot_mask > 0)
                skeleton = skel_temp.astype(np.float32)
                # Fallback if skeletonize produces an empty mask
                if np.sum(skeleton) == 0:
                    props = regionprops(spot_mask.astype(np.uint8))
                    if props:
                        cy, cx = props[0].centroid
                        self._place_centroid_skeleton_point(skeleton, spot_region, cy, cx)
            except Exception:
                # Final fallback to centroid
                props = regionprops(spot_mask.astype(np.uint8))
                if props:
                    cy, cx = props[0].centroid
                    self._place_centroid_skeleton_point(skeleton, spot_region, cy, cx)

        # --- 2. Generate boundary ---
        kernel = np.ones((3, 3), np.uint8)
        eroded = cv2.erode(spot_mask.astype(np.uint8), kernel, iterations=1)
        boundary = (spot_mask.astype(np.uint8) - eroded).astype(np.float32)

        # --- 3. Generate SDT (Corrected Logic) ---
        # Distance to boundary (increases from boundary inward)
        dist_to_boundary = distance_transform_edt(spot_mask)

        # --- CRITICAL FIX: Internal Distance to Skeleton ---
        skeleton_binary = skeleton > 0.5 # Use a threshold for binary conversion
        dist_to_skeleton_internal = np.full((H, W), np.inf, dtype=np.float32) # Init with inf

        if np.any(spot_region) and np.any(skeleton_binary):
            # Get coordinates of spot pixels and skeleton pixels
            coords_spot = np.argwhere(spot_region)
            coords_skel = np.argwhere(skeleton_binary)
            if len(coords_skel) > 0:
                # Calculate distance from each spot pixel to the nearest skeleton pixel
                dists = cdist(coords_spot, coords_skel, 'euclidean')
                min_dists = np.min(dists, axis=1)
                # Place the minimum distances back into the full map at the correct spot pixel locations
                for (y, x), d in zip(coords_spot, min_dists):
                    dist_to_skeleton_internal[y, x] = d
            else:
                # If no skeleton pixels found (should be rare with fallbacks, but be safe)
                # Treat internal distance as 0 everywhere (or max dist?)
                dist_to_skeleton_internal[spot_region] = 0.0
        else:
            # If no spot or no skeleton, internal distance is irrelevant, set to 0 or inf?
            # Let's keep it inf as initialized, SDT logic handles it.
            pass

        # Normalize internal distance to skeleton (0 at skeleton, increasing outward within spot)
        # CRITICAL FIXES HERE:
        # 1. Handle potential inf or nan in min_dists
        # 2. Ensure max_skel_dist is not 0 or inf
        max_skel_dist = np.max(dist_to_skeleton_internal[spot_region]) if np.any(spot_region) else 0.0
        # Add a small epsilon to denominator and handle case where max is 0 or inf
        eps_for_norm = 1e-6
        if not np.isfinite(max_skel_dist) or max_skel_dist <= eps_for_norm:
            # If max distance is not meaningful, normalize differently or set to 0
            # A reasonable fallback is to make skeleton_norm 0 everywhere inside the spot
            # This means (1 - d_skel) = 1, so SDT = d_boundary * 1^alpha = d_boundary
            skeleton_norm = np.zeros_like(spot_mask, dtype=np.float32)
        else:
            skeleton_norm = dist_to_skeleton_internal / (max_skel_dist + eps_for_norm)
        
        # Ensure skeleton_norm is 0 outside the spot and clipped correctly inside
        skeleton_norm[~spot_region] = 0.0
        skeleton_norm = np.clip(skeleton_norm, 0.0, 1.0) # Extra safety clip

        # --- END CRITICAL FIX ---
        # Normalize distance to boundary
        if np.any(spot_region):
            max_boundary_dist = np.max(dist_to_boundary[spot_region]) + 1e-6
            boundary_norm = dist_to_boundary / max_boundary_dist
            boundary_norm[~spot_region] = 0  # Ensure outside spot is 0
            
            # SDT formula (aligning with paper intent)
            alpha = 1.5 if spot_area <= 15 else 1.2
            # SDT(x) = d_boundary(x) * (1 - d_skel(x))^α
            # Where d_skel(x) is normalized internal distance (0 at skel, 1 far away)
            sdt = boundary_norm * np.power(np.clip(1.0 - skeleton_norm, 0, 1), alpha)
            sdt = np.clip(sdt, 0, 1) # Final clamp for safety
        else:
            sdt = np.zeros_like(spot_mask, dtype=np.float32)

        return sdt, skeleton, boundary

    def generate_centroid_map(self, shape, centroids):
        """Generate a centroid heatmap with Gaussian peaks at spot centers."""
        centroid_map = np.zeros(shape, dtype=np.float32)
        sigma = 1.0  # Controls the spread of the Gaussian
        for cy, cx in centroids:
            cx_int, cy_int = int(round(cx)), int(round(cy))
            # Bounding box around the peak
            x_min, x_max = max(0, cx_int - 6), min(shape[1], cx_int + 7)
            y_min, y_max = max(0, cy_int - 6), min(shape[0], cy_int + 7)
            Y, X = np.ogrid[y_min:y_max, x_min:x_max]
            g = np.exp(-((X - cx)**2 + (Y - cy)**2) / (2 * sigma**2))
            # Use np.maximum to handle overlapping Gaussians correctly
            existing = centroid_map[y_min:y_max, x_min:x_max]
            centroid_map[y_min:y_max, x_min:x_max] = np.maximum(existing, g)
        return centroid_map

    def generate_flow_field_from_centroids(self, semantic_mask, centroids):
        """
        Generate a 2-channel flow field pointing from each pixel to its nearest centroid.
        Ensures unit vectors for stability.
        """
        h, w = semantic_mask.shape
        if len(centroids) == 0:
            return np.zeros((2, h, w), dtype=np.float32)

        flow_field = np.zeros((2, h, w), dtype=np.float32)
        # Get boolean mask for all spot pixels
        spot_mask_flat = semantic_mask.flatten()
        spot_indices = np.where(spot_mask_flat > 0)[0]
        if len(spot_indices) == 0:
            return flow_field

        # Get coordinates of all spot pixels
        y_coords, x_coords = np.unravel_index(spot_indices, (h, w))
        pixel_coords = np.column_stack((y_coords, x_coords)) # Shape (N, 2)

        centroids_arr = np.array(centroids)
        if len(centroids_arr) == 0:
            return flow_field

        # Calculate distances for all spot pixels at once to all centroids
        distances = cdist(pixel_coords, centroids_arr) # Shape (N, num_centroids)
        nearest_idx = np.argmin(distances, axis=1) # Shape (N,)

        # Calculate flow vectors (unit vectors pointing to nearest centroid)
        nearest_centroids = centroids_arr[nearest_idx] # Shape (N, 2)
        delta = nearest_centroids - pixel_coords # Shape (N, 2) - (dy, dx)
        norms = np.linalg.norm(delta, axis=1, keepdims=True) # Shape (N, 1)
        norms = np.where(norms == 0, 1, norms) # Avoid division by zero
        unit_vectors = delta / norms # Shape (N, 2) - normalized flow vectors (dy, dx)

        # Assign flow vectors back to the full flow field array
        flow_field[0, y_coords, x_coords] = unit_vectors[:, 0] # dy component
        flow_field[1, y_coords, x_coords] = unit_vectors[:, 1] # dx component

        # CRITICAL FIX: Ensure final flow vectors are unit vectors
        # Numerical errors might cause slight deviations
        final_norms = np.linalg.norm(flow_field, axis=0, keepdims=True) # Shape (1, H, W)
        # Avoid division by zero
        final_norms_safe = np.where(final_norms > 1e-12, final_norms, 1.0)
        # Normalize
        flow_field_normalized = flow_field / final_norms_safe
        # Final clamp to ensure magnitude <= 1.0
        # Magnitude squared
        mag_sq = np.sum(flow_field_normalized**2, axis=0, keepdims=True)
        # Scale down if necessary
        scale_down = np.where(mag_sq > 1.0, np.sqrt(mag_sq), 1.0)
        flow_field_final = flow_field_normalized / scale_down

        return flow_field_final

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        # --- 1. Load and Preprocess Image ---
        image = tifffile.imread(self.image_paths[idx]).astype(np.float32)
        mask = tifffile.imread(self.mask_paths[idx]).astype(np.int32)
        # Robust percentile-based normalization
        vmin, vmax = np.percentile(image, (1, 99))
        image = np.clip((image - vmin) / (vmax - vmin + 1e-8), 0, 1)
        H, W = image.shape

        # --- 2. Smart Patch Extraction ---
        valid_ids = self._get_valid_spots(mask)
        # Prefer patches containing spots
        if len(valid_ids) > 0 and random.random() < 0.7:
            spot_id = random.choice(valid_ids)
            coords = np.argwhere(mask == spot_id)
            # Calculate centroid of the selected spot
            cy, cx = coords.mean(axis=0).astype(int) # cy is row (y), cx is col (x)
            # --- CORRECTED Patch Extraction Coordinates ---
            # Use cy (row) and cx (col) correctly
            y0 = np.clip(cy - self.patch_size // 2, 0, H - self.patch_size)
            x0 = np.clip(cx - self.patch_size // 2, 0, W - self.patch_size)
        else:
            # Random patch
            y0 = random.randint(0, max(0, H - self.patch_size))
            x0 = random.randint(0, max(0, W - self.patch_size))

        # --- CORRECTED Patch Extraction (y first, then x) ---
        patch_img = image[y0:y0+self.patch_size, x0:x0+self.patch_size]
        patch_mask = mask[y0:y0+self.patch_size, x0:x0+self.patch_size]

        # Padding if patch goes beyond image boundaries (should be rare with clipping)
        if patch_img.shape[0] < self.patch_size or patch_img.shape[1] < self.patch_size:
            ph = max(0, self.patch_size - patch_img.shape[0])
            pw = max(0, self.patch_size - patch_img.shape[1])
            patch_img = np.pad(patch_img, ((0, ph), (0, pw)), 'reflect')
            patch_mask = np.pad(patch_mask, ((0, ph), (0, pw)), 'constant')

        # --- 3. Process Targets ---
        valid_ids = self._get_valid_spots(patch_mask)
        if len(valid_ids) == 0:
            # Return zero tensors if no spots are in the patch
            return (torch.zeros((1, self.patch_size, self.patch_size), dtype=torch.float32),
                    torch.zeros((7, self.patch_size, self.patch_size), dtype=torch.float32))

        centroids, spot_masks = self._extract_spot_info(patch_mask, valid_ids)
        semantic = self._generate_semantic_mask(spot_masks, patch_mask.shape)

        # Combine SDT, Skeleton, and Boundary for all spots in the patch
        combined_sdt = np.zeros_like(semantic, dtype=np.float32)
        combined_skeleton = np.zeros_like(semantic, dtype=np.float32)
        combined_boundary = np.zeros_like(semantic, dtype=np.float32)

        for spot_mask in spot_masks:
            sdt, skeleton, boundary = self.generate_skeleton_aware_distance_transform(spot_mask)
            # Use np.maximum to combine, taking the highest value at each pixel
            # This handles overlapping or touching spots reasonably
            combined_sdt = np.maximum(combined_sdt, sdt)
            combined_skeleton = np.maximum(combined_skeleton, skeleton)
            combined_boundary = np.maximum(combined_boundary, boundary)

        centroid_map = self.generate_centroid_map(patch_mask.shape, centroids)
        flow_field = self.generate_flow_field_from_centroids(semantic, centroids) # Shape: (2, H, W)

        # --- 4. Stack Targets ---
        # Order: [semantic, sdt, skeleton, centroid, flow_y, flow_x, boundary]
        targets = np.stack([
            semantic,
            combined_sdt,
            combined_skeleton,
            centroid_map,
            flow_field[0], # flow_y
            flow_field[1], # flow_x
            combined_boundary
        ], axis=0)

        # --- 5. Apply Augmentations ---
        if self.transform is not None:
            # Move channels to the end for Albumentations (H, W, C)
            targets_hwc = np.moveaxis(targets, 0, -1)
            augmented = self.transform(image=patch_img, mask=targets_hwc)
            patch_img = augmented['image']
            targets = np.moveaxis(augmented['mask'], -1, 0) # Move channels back to front (C, H, W)

            # Post-augmentation cleanup to ensure value ranges are correct
            # These are critical to prevent GradNorm explosion
            targets[1] = np.clip(targets[1], 0, 1)  # SDT - MUST BE [0, 1]
            targets[2] = np.clip(targets[2], 0, 1)  # Skeleton - preserve gradients, but clip!
            targets[0] = (targets[0] > 0.5).astype(np.float32)  # Semantic - MUST BE binary
            targets[6] = (targets[6] > 0.5).astype(np.float32)  # Boundary - MUST BE binary
            # Flow (channels 4, 5) should ideally be in [-1, 1], but clipping to [-1.1, 1.1] is safer post-augmentation
            targets[4] = np.clip(targets[4], -1.1, 1.1) # Flow Y
            targets[5] = np.clip(targets[5], -1.1, 1.1) # Flow X
            # Centroid map (channel 3) should be [0, 1]
            targets[3] = np.clip(targets[3], 0, 1) # Centroid

        # --- 6. Convert to Tensors ---
        img_tensor = torch.from_numpy(patch_img.astype(np.float32)).unsqueeze(0) # Add channel dim
        targets_tensor = torch.from_numpy(targets.astype(np.float32))

        return img_tensor, targets_tensor

print("SkeletonAwareSpotDataset class is ready.")




# Cell: Full-Image Multi-Channel Visualization for Skeleton-Aware Spot Detection
import os
import matplotlib.pyplot as plt
import numpy as np
import tifffile
from scipy.ndimage import distance_transform_edt
from scipy.spatial.distance import cdist
from skimage.measure import regionprops
from skimage.morphology import skeletonize
import cv2

# --- 1. SDT Generation for a Single Spot ---
def generate_skeleton_aware_distance_transform(spot_mask):
    if not np.any(spot_mask):
        return (np.zeros_like(spot_mask, dtype=np.float32),
                np.zeros_like(spot_mask, dtype=np.float32),
                np.zeros_like(spot_mask, dtype=np.float32))
    
    H, W = spot_mask.shape
    spot_area = np.sum(spot_mask)
    
    # --- Generate Skeleton ---
    skeleton = np.zeros_like(spot_mask, dtype=np.float32)
    if spot_area <= 10:
        props = regionprops(spot_mask.astype(np.uint16))
        if props:
            cy, cx = map(int, props[0].centroid)
            Y, X = np.ogrid[:H, :W]
            dist_from_center = np.sqrt((Y - cy)**2 + (X - cx)**2)
            skeleton[spot_mask > 0] = np.clip(1.0 - dist_from_center[spot_mask > 0] / 2.0, 0, 1)
    else:
        try:
            skel_temp = skeletonize(spot_mask > 0)
            skeleton = skel_temp.astype(np.float32)
            if np.sum(skeleton) == 0:
                props = regionprops(spot_mask.astype(np.uint16))
                if props:
                    cy, cx = map(int, props[0].centroid)
                    if 0 <= cy < H and 0 <= cx < W:
                        skeleton[cy, cx] = 1.0
        except Exception:
            props = regionprops(spot_mask.astype(np.uint16))
            if props:
                cy, cx = map(int, props[0].centroid)
                if 0 <= cy < H and 0 <= cx < W:
                    skeleton[cy, cx] = 1.0

    # --- Generate Boundary ---
    kernel = np.ones((3, 3), np.uint8)
    eroded = cv2.erode(spot_mask.astype(np.uint8), kernel, iterations=1)
    boundary = (spot_mask.astype(np.uint8) - eroded).astype(np.float32)

    # --- Generate SDT ---
    spot_region = spot_mask > 0
    dist_to_boundary = distance_transform_edt(spot_mask)

    skeleton_binary = skeleton > 0
    if np.any(spot_region) and np.any(skeleton_binary):
        coords_spot = np.argwhere(spot_region)
        coords_skel = np.argwhere(skeleton_binary)
        if len(coords_skel) > 0:
            dists = cdist(coords_spot, coords_skel, 'euclidean')
            min_dists = np.min(dists, axis=1)
            dist_to_skeleton_internal = np.full((H, W), np.inf, dtype=np.float32)
            for (y, x), d in zip(coords_spot, min_dists):
                dist_to_skeleton_internal[y, x] = d
            max_skel_dist = np.max(min_dists) if len(min_dists) > 0 else 1.0
            skeleton_norm = dist_to_skeleton_internal / (max_skel_dist + 1e-6)
            skeleton_norm[~spot_region] = 0
        else:
            skeleton_norm = np.zeros_like(spot_mask, dtype=np.float32)
    else:
        skeleton_norm = np.zeros_like(spot_mask, dtype=np.float32)

    if np.any(spot_region):
        max_boundary_dist = np.max(dist_to_boundary[spot_region]) + 1e-6
        boundary_norm = dist_to_boundary / max_boundary_dist
        boundary_norm[~spot_region] = 0
        alpha = 1.5 if spot_area <= 15 else 1.2
        sdt = boundary_norm * np.power(np.clip(1.0 - skeleton_norm, 0, 1), alpha)
        sdt = np.clip(sdt, 0, 1)
    else:
        sdt = np.zeros_like(spot_mask, dtype=np.float32)

    return sdt, skeleton, boundary


# --- 2. Gaussian Centroid Map for Multiple Spots ---
def generate_centroid_map(shape, centroids):
    centroid_map = np.zeros(shape, dtype=np.float32)
    sigma = 1.0
    for cy, cx in centroids:
        cy_int, cx_int = int(round(cy)), int(round(cx))
        y_min = max(0, cy_int - 6)
        y_max = min(shape[0], cy_int + 7)
        x_min = max(0, cx_int - 6)
        x_max = min(shape[1], cx_int + 7)
        if y_min >= y_max or x_min >= x_max:
            continue
        Y, X = np.ogrid[y_min:y_max, x_min:x_max]
        g = np.exp(-((X - cx)**2 + (Y - cy)**2) / (2 * sigma**2))
        existing = centroid_map[y_min:y_max, x_min:x_max]
        centroid_map[y_min:y_max, x_min:x_max] = np.maximum(existing, g)
    return centroid_map


# --- 3. FULL-IMAGE MULTI-CHANNEL VISUALIZATION ---
def visualize_full_image_analysis(image_path, mask_path, output_dir="full_image_analysis"):
    """
    Create a 3x3 grid showing all model targets for the entire image.
    No individual spot analysis — just full-image overviews.
    """
    os.makedirs(output_dir, exist_ok=True)

    # --- 1. Load 16-bit Image and Mask ---
    image = tifffile.imread(image_path).astype(np.float32)
    mask = tifffile.imread(mask_path).astype(np.uint16)  # Ensure 16-bit
    vmin, vmax = np.percentile(image, (1, 99))
    image_norm = np.clip((image - vmin) / (vmax - vmin + 1e-8), 0, 1)

    # --- 2. Extract All Valid Spots (16-bit safe) ---
    valid_ids = np.unique(mask)
    valid_ids = valid_ids[valid_ids > 0]  # Remove background
    print(f"Found {len(valid_ids)} spots in the mask (16-bit safe).")

    # Initialize accumulators
    H, W = mask.shape
    combined_semantic = np.zeros((H, W), dtype=np.float32)
    combined_skeleton = np.zeros((H, W), dtype=np.float32)
    combined_boundary = np.zeros((H, W), dtype=np.float32)
    combined_sdt = np.zeros((H, W), dtype=np.float32)
    all_centroids = []

    # --- 3. Process All Spots ---
    for spot_id in valid_ids:
        spot_mask = (mask == spot_id).astype(np.uint16)
        if np.sum(spot_mask) == 0:
            continue

        # Extract centroid
        props = regionprops(spot_mask)
        if props:
            cy, cx = props[0].centroid
            all_centroids.append((cy, cx))

        # Generate maps
        sdt, skeleton, boundary = generate_skeleton_aware_distance_transform(spot_mask)
        combined_semantic = np.maximum(combined_semantic, spot_mask.astype(np.float32))
        combined_skeleton = np.maximum(combined_skeleton, skeleton)
        combined_boundary = np.maximum(combined_boundary, boundary)
        combined_sdt = np.maximum(combined_sdt, sdt)

    # --- 4. Generate Gaussian Centroid Map ---
    centroid_map = generate_centroid_map((H, W), all_centroids)
    print(f"Generated Gaussian centroid map with {len(all_centroids)} peaks.")

    # --- 5. Instance Segmentation (Optional) ---
    from skimage.segmentation import watershed
    from scipy.ndimage import maximum_filter
    peaks = (centroid_map > 0.3) & (maximum_filter(centroid_map, size=3) == centroid_map)
    seeds = np.zeros((H, W), dtype=np.int32)
    if len(all_centroids) > 0:
        for idx, (y, x) in enumerate(all_centroids, 1):
            if 0 <= y < H and 0 <= x < W:
                seeds[int(y), int(x)] = idx
    instance_labels = watershed(-combined_sdt, seeds, mask=(combined_semantic > 0.5))

    # --- 6. Create 3x3 Visualization ---
    fig, axes = plt.subplots(3, 3, figsize=(20, 18))
    fig.suptitle("Full-Image Multi-Channel Analysis", fontsize=18)

    # 1. Input Image
    axes[0, 0].imshow(image_norm, cmap='gray')
    axes[0, 0].set_title("Input Image")
    axes[0, 0].axis('off')

    # 2. Semantic Mask (All Spots)
    axes[0, 1].imshow(combined_semantic, cmap='viridis')
    axes[0, 1].set_title("Semantic Mask (All Spots)")
    axes[0, 1].axis('off')

    # 3. Skeleton (All Spots)
    axes[0, 2].imshow(combined_skeleton, cmap='bone')
    axes[0, 2].set_title("Skeleton (All Spots)")
    axes[0, 2].axis('off')

    # 4. Boundary (All Spots)
    axes[1, 0].imshow(combined_boundary, cmap='gray')
    axes[1, 0].set_title("Boundary (All Spots)")
    axes[1, 0].axis('off')

    # 5. SDT (All Spots)
    im5 = axes[1, 1].imshow(combined_sdt, cmap='magma')
    axes[1, 1].set_title("SDT (All Spots)")
    plt.colorbar(im5, ax=axes[1, 1], shrink=0.8)

    # 6. Gaussian Centroid Map (All Spots)
    im6 = axes[1, 2].imshow(centroid_map, cmap='coolwarm')
    axes[1, 2].set_title("Gaussian Centroid Map (All Spots)")
    plt.colorbar(im6, ax=axes[1, 2], shrink=0.8)

    # 7. Spot + Skeleton Overlay
    axes[2, 0].imshow(combined_semantic, cmap='gray')
    axes[2, 0].imshow(combined_skeleton, cmap='Reds', alpha=0.6)
    axes[2, 0].set_title("Spot + Skeleton Overlay")
    axes[2, 0].axis('off')

    # 8. Spot + Centroid Overlay
    axes[2, 1].imshow(combined_semantic, cmap='gray')
    axes[2, 1].imshow(centroid_map, cmap='coolwarm', alpha=0.6)
    y_coords, x_coords = zip(*all_centroids) if all_centroids else ([], [])
    axes[2, 1].scatter(x_coords, y_coords, c='red', s=30, marker='x', label='Centroid')
    axes[2, 1].legend()
    axes[2, 1].set_title("Spot + Centroid Overlay")
    axes[2, 1].axis('off')

    # 9. Instance Labels
    axes[2, 2].imshow(instance_labels, cmap='nipy_spectral')
    axes[2, 2].set_title("Instance Labels")
    axes[2, 2].axis('off')

    plt.tight_layout()
    plot_filename = os.path.join(output_dir, "full_image_analysis_grid.png")
    plt.savefig(plot_filename, dpi=150, bbox_inches='tight')
    plt.close()

    print(f"✅ Full-image analysis saved to: {plot_filename}")
    print(f"✅ Total spots processed: {len(valid_ids)}")
    print(f"✅ Centroids aligned: {len(all_centroids)}")


# --- 4. Example Usage ---
if __name__ == "__main__":
    image_path = "/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/images/synthetic_000003.tif"
    mask_path = "/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/masks/synthetic_000003.tif"
    
    visualize_full_image_analysis(image_path, mask_path)
    print("\n🎉 Full-image analysis complete.")

# Cell: Updated Data Loader Creation (Fix Pin Memory)
def create_skeleton_aware_data_loaders_fixed(image_paths, mask_paths, batch_size=12, 
                                             patch_size=256, num_workers=4):
    """
    Fixed data loader creation without pin_memory to avoid CUDA OOM errors.
    """
    # Validate dataset
    assert len(image_paths) == len(mask_paths), "Image and mask counts must match"
    assert all(os.path.exists(p) for p in image_paths), "Some image paths are invalid"
    assert all(os.path.exists(p) for p in mask_paths), "Some mask paths are invalid"
    
    # Split data
    train_imgs, val_imgs, train_masks, val_masks = train_test_split(
        image_paths, mask_paths, test_size=0.2, random_state=42, shuffle=True
    )
    
    print(f"Dataset split: {len(train_imgs)} training, {len(val_imgs)} validation samples")
    
    # Datasets
    train_dataset = SkeletonAwareSpotDataset(
        train_imgs, train_masks,
        transform=get_optimized_transforms(),
        patch_size=patch_size
    )
    val_dataset = SkeletonAwareSpotDataset(
        val_imgs, val_masks,
        transform=None,
        patch_size=patch_size
    )
    
    # Worker initialization function for deterministic behavior
    def worker_init_fn(worker_id):
        worker_seed = torch.initial_seed() % 2**32
        np.random.seed(worker_seed)
        random.seed(worker_seed)
    
    # Calculate optimal prefetch factor
    prefetch_factor = max(2, batch_size // 4)
    
    # --- FIX: Disable pin_memory to prevent CUDA OOM ---
    use_pin_memory = False # Set to False to avoid pin memory errors
    
    # DataLoaders with optimized settings
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=use_pin_memory, # Use the fixed value
        drop_last=True,
        prefetch_factor=prefetch_factor,
        persistent_workers=True if num_workers > 0 else False,
        worker_init_fn=worker_init_fn
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=max(0, num_workers // 2),
        pin_memory=use_pin_memory, # Use the fixed value
        drop_last=False,
        prefetch_factor=prefetch_factor,
        persistent_workers=True if num_workers > 0 else False
    )
    
    # Verify first batch loads correctly (without pin_memory)
    try:
        images, targets = next(iter(train_loader))
        print(f"Data loader verified: Batch shape - images {images.shape}, targets {targets.shape}")
        print(f"Data types: images {images.dtype}, targets {targets.dtype}")
    except Exception as e:
        print(f"WARNING: Data loader verification failed: {e}")
    
    return train_loader, val_loader

# Update your main training script to use this function:
# train_loader, val_loader = create_skeleton_aware_data_loaders_fixed(
#     image_paths, mask_paths, 
#     batch_size=12, 
#     patch_size=256, 
#     num_workers=4
# )

# Cell 3: Model - Corrected and Improved
class EfficientBlock(nn.Module):
    def __init__(self, in_ch, out_ch, stride=1, expand_ratio=4, dropout_prob=0.1):
        super().__init__()
        hidden_ch = in_ch * expand_ratio
        self.stride = stride
        # Pointwise expansion
        if expand_ratio != 1:
            self.expand = nn.Sequential(
                nn.Conv2d(in_ch, hidden_ch, 1, bias=False),
                nn.BatchNorm2d(hidden_ch),
                nn.SiLU(inplace=True)
            )
        else:
            self.expand = nn.Identity()
        # Depthwise convolution
        self.depthwise = nn.Sequential(
            nn.Conv2d(hidden_ch, hidden_ch, 3, stride, 1, groups=hidden_ch, bias=False),
            nn.BatchNorm2d(hidden_ch),
            nn.SiLU(inplace=True)
        )
        # Squeeze-and-excitation
        self.se = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(hidden_ch, max(1, hidden_ch//4), 1),
            nn.SiLU(inplace=True),
            nn.Conv2d(max(1, hidden_ch//4), hidden_ch, 1),
            nn.Sigmoid()
        )
        # Pointwise projection
        self.project = nn.Sequential(
            nn.Conv2d(hidden_ch, out_ch, 1, bias=False),
            nn.BatchNorm2d(out_ch)
        )
        self.skip = (stride == 1 and in_ch == out_ch)
        self.dropout = nn.Dropout2d(dropout_prob) if stride != 1 and in_ch != out_ch else nn.Identity()

    def forward(self, x):
        residual = x
        x = self.expand(x)
        x = self.depthwise(x)
        x = x * self.se(x)
        x = self.project(x)
        x = self.dropout(x)
        if self.skip:
            scale = 1.0 / (2.0 ** 0.5)
            return scale * (x + residual)
        return x

class SimpleSpatialAttention(nn.Module):
    def __init__(self, in_channels):
        super().__init__()
        self.conv = nn.Conv2d(in_channels, 1, kernel_size=7, padding=3, bias=False)
        self.sigmoid = nn.Sigmoid()
        self.bn = nn.BatchNorm2d(1)

    def forward(self, x):
        attn = self.conv(x)
        attn = self.bn(attn)
        attn = self.sigmoid(attn)
        return x * attn

class SkeletonAwareSpotDetector(nn.Module):
    def __init__(self, in_ch=1, base_ch=48, dropout_prob=0.1):
        super().__init__()
        # --- Stem ---
        self.stem_conv1 = nn.Sequential(
            nn.Conv2d(in_ch, base_ch, 3, padding=1, bias=False),
            nn.BatchNorm2d(base_ch),
            nn.SiLU(inplace=True)
        )
        self.stem_conv2 = nn.Sequential(
            nn.Conv2d(base_ch, base_ch, 3, padding=1, bias=False),
            nn.BatchNorm2d(base_ch),
            nn.SiLU(inplace=True),
            nn.Dropout2d(dropout_prob)
        )
        # --- Encoder ---
        self.enc1 = nn.Sequential(
            EfficientBlock(base_ch, base_ch*2, stride=2, dropout_prob=dropout_prob),
            EfficientBlock(base_ch*2, base_ch*2, dropout_prob=dropout_prob)
        )
        self.enc2 = nn.Sequential(
            EfficientBlock(base_ch*2, base_ch*4, stride=2, dropout_prob=dropout_prob),
            EfficientBlock(base_ch*4, base_ch*4, dropout_prob=dropout_prob)
        )
        # --- FPN ---
        self.fpn_lateral_stem = nn.Conv2d(base_ch, base_ch, 1)
        self.fpn_conv2 = nn.Sequential(
            nn.Conv2d(base_ch*4, base_ch*2, 1),
            nn.BatchNorm2d(base_ch*2),
            nn.SiLU(inplace=True)
        )
        self.fpn_conv1 = nn.Sequential(
            nn.Conv2d(base_ch*2, base_ch, 1),
            nn.BatchNorm2d(base_ch),
            nn.SiLU(inplace=True)
        )
        self.fusion_conv = nn.Sequential(
            nn.Conv2d(base_ch*2, base_ch, 1),
            nn.BatchNorm2d(base_ch),
            nn.SiLU(inplace=True)
        )
        self.skip_conv1 = nn.Conv2d(base_ch, base_ch, 1)
        self.skip_conv2 = nn.Conv2d(base_ch, base_ch, 1)
        # --- Decoder ---
        self.decoder = nn.Sequential(
            EfficientBlock(base_ch*3, base_ch*2, dropout_prob=dropout_prob),
            EfficientBlock(base_ch*2, base_ch, dropout_prob=dropout_prob),
            EfficientBlock(base_ch, base_ch, dropout_prob=dropout_prob),
            nn.Conv2d(base_ch, base_ch, 3, padding=1, bias=False),
            nn.BatchNorm2d(base_ch),
            nn.SiLU(inplace=True),
            nn.Dropout2d(dropout_prob)
        )
        # --- Attention ---
        self.spatial_attn = SimpleSpatialAttention(base_ch)
        # --- Shared head ---
        self.shared_head = nn.Sequential(
            nn.Conv2d(base_ch, base_ch, 3, padding=1, groups=base_ch, bias=False),
            nn.BatchNorm2d(base_ch),
            nn.SiLU(inplace=True),
            nn.Conv2d(base_ch, base_ch, 1, bias=False),
            nn.BatchNorm2d(base_ch),
            nn.SiLU(inplace=True)
        )
        # --- Output heads ---
        # SDT head (11 bins)
        self.sdt_head = nn.Sequential(
            nn.Conv2d(base_ch, base_ch, 3, padding=1, bias=False),
            nn.BatchNorm2d(base_ch),
            nn.SiLU(inplace=True),
            nn.Conv2d(base_ch, 11, 1)  # 10 bins + 1 background
        )
        # Skeleton head (1 channel)
        self.skeleton_head = nn.Sequential(
            nn.Conv2d(base_ch, base_ch, 3, padding=1, bias=False),
            nn.BatchNorm2d(base_ch),
            nn.SiLU(inplace=True),
            nn.Conv2d(base_ch, 1, 1)
        )
        # Semantic head (1 channel, sigmoid)
        self.semantic_head = nn.Sequential(nn.Conv2d(base_ch, 1, 1), nn.Sigmoid())
        # Centroid head (1 channel, sigmoid)
        self.centroid_head = nn.Sequential(nn.Conv2d(base_ch, 1, 1), nn.Sigmoid())
        # Flow head (2 channels)
        self.flow_head = nn.Conv2d(base_ch, 2, 1)  # 2-channel flow field

        # --- NEW: Boundary head (1 channel, sigmoid) ---
        self.boundary_head = nn.Sequential(
            nn.Conv2d(base_ch, base_ch, 3, padding=1, bias=False),
            nn.BatchNorm2d(base_ch),
            nn.SiLU(inplace=True),
            nn.Conv2d(base_ch, 1, 1),
            nn.Sigmoid() # Sigmoid for binary boundary map
        )
        # --- END NEW ---

        self._init_weights()

    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None: nn.init.zeros_(m.bias)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Linear):
                nn.init.trunc_normal_(m.weight, std=0.02)
                if m.bias is not None: nn.init.zeros_(m.bias)
            # Specific initializations for output heads if needed
            elif isinstance(m, nn.Conv2d) and m.out_channels == 11:
                # SDT head initialization
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='linear')
                if m.bias is not None: nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Conv2d) and m.out_channels == 2:
                # Flow head initialization
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu', a=0.1)
                if m.bias is not None: nn.init.zeros_(m.bias)
            # The new boundary head uses default init, which is fine.
            # If you want a specific init for the final conv layer of boundary_head:
            # elif isinstance(m, nn.Conv2d) and m is self.boundary_head[-2]: # Check if it's the last Conv2d before Sigmoid
            #     nn.init.xavier_uniform_(m.weight)
            #     if m.bias is not None: nn.init.zeros_(m.bias)

    def forward(self, x):
        B, C, H, W = x.shape
        # --- Encoder ---
        x0 = self.stem_conv2(self.stem_conv1(x))
        x1 = self.enc1(x0)
        x2 = self.enc2(x1)
        # --- FPN ---
        p2 = self.fpn_conv2(x2)
        p2_up = F.interpolate(p2, size=(H//2, W//2), mode='bilinear', align_corners=False)
        p1_in = x1 + p2_up
        p1 = self.fpn_conv1(p1_in)
        p1_up = F.interpolate(p1, size=(H, W), mode='bilinear', align_corners=False)
        lateral_x0 = self.fpn_lateral_stem(x0)
        fused_features = torch.cat([lateral_x0, p1_up], dim=1)
        p1_up_final = self.fusion_conv(fused_features)
        # --- Attention & Fusion ---
        attention_map = torch.sigmoid(torch.mean(p1_up_final, dim=1, keepdim=True))
        attended = x0 * (0.5 + 0.5 * attention_map)
        x0_scaled = self.skip_conv1(x0)
        p1_up_scaled = self.skip_conv2(p1_up_final)
        fused = torch.cat([x0_scaled, p1_up_scaled, attended], dim=1)
        # --- Decoder ---
        features = self.decoder(fused)
        features = self.spatial_attn(features)
        shared_feat = self.shared_head(features)
        # --- Outputs ---
        sdt_out = self.sdt_head(shared_feat)
        skeleton_out = self.skeleton_head(shared_feat)
        flow_out = self.flow_head(shared_feat)
        # --- Flow magnitude constraint ---
        flow_magnitude = torch.sqrt(torch.sum(flow_out ** 2, dim=1, keepdim=True) + 1e-6)
        scale = torch.min(torch.ones_like(flow_magnitude), 1.5 / (flow_magnitude + 1e-6))
        constrained_flow_out = flow_out * scale

        # --- NEW: Boundary output ---
        boundary_out = self.boundary_head(shared_feat)
        # --- END NEW ---

        return {
            'sem_out': self.semantic_head(shared_feat),
            'sdt_out': sdt_out,
            'skeleton_out': skeleton_out,
            'hm_out': self.centroid_head(shared_feat),
            'flow_out': constrained_flow_out,
            # --- NEW: Include boundary output ---
            'boundary_out': boundary_out
            # --- END NEW ---
        }

print("✅ SkeletonAwareSpotDetector model updated with 'boundary_out' head.")



import torch
import torch.nn as nn
import torch.nn.functional as F

class QuantizedSDTLoss(nn.Module):
    """
    Original Quantized SDT Loss using Cross-Entropy.
    Calculates cross-entropy loss between predicted SDT logits and quantized target classes.
    """
    def __init__(self, num_bins=10, background_value=0.0):
        """
        Initializes the QuantizedSDTLoss module.

        Args:
            num_bins (int): Number of bins for quantizing the SDT target. Default: 10.
            background_value (float): Value to assign to background pixels in target. Default: 0.0.
        """
        super().__init__()
        self.num_bins = num_bins
        self.background_value = background_value
        self.eps = 1e-6

    def forward(self, pred, target, mask=None):
        """
        Forward pass to compute the quantized SDT loss.

        Args:
            pred (torch.Tensor): Predicted SDT logits of shape (B, num_bins, H, W).
            target (torch.Tensor): Ground truth SDT scalar map of shape (B, 1, H, W).
            mask (torch.Tensor, optional): Semantic mask of shape (B, 1, H, W) to focus loss.
                                             Default: None (no masking).

        Returns:
            torch.Tensor: Computed loss (scalar).
        """
        # Squeeze the target channel dimension: (B, 1, H, W) -> (B, H, W)
        target_sq = target.squeeze(1)

        if mask is not None:
            # Squeeze the mask channel dimension: (B, 1, H, W) -> (B, H, W)
            mask_sq = mask.squeeze(1)
            # Initialize target classes tensor with background class (0)
            target_classes = torch.zeros_like(target_sq, dtype=torch.long)

            # Quantize the target SDT values into bins
            # Bins are [0/num_bins, 1/num_bins), ..., [(num_bins-1)/num_bins, num_bins/num_bins=1.0)
            for i in range(self.num_bins):
                bin_min = i / self.num_bins
                bin_max = (i + 1) / self.num_bins
                # Create a mask for pixels belonging to this bin AND within the semantic mask
                bin_mask = (target_sq > bin_min) & (target_sq <= bin_max) & (mask_sq > 0)
                # Assign class label (1-indexed) to these pixels
                target_classes[bin_mask] = i + 1
        else:
            # If no mask, quantize the entire target
            # Clamp and scale target, then convert to long for class indices
            # This maps [0.0, 1.0] to [0, num_bins] classes
            target_classes = torch.clamp((target_sq * self.num_bins).long(), 0, self.num_bins)

        # Compute per-pixel cross-entropy loss
        # pred shape: (B, num_bins, H, W)
        # target_classes shape: (B, H, W)
        base_loss = F.cross_entropy(pred, target_classes, reduction='none')

        # Apply boundary weighting: emphasize pixels near boundaries (low SDT values)
        # target_sq is the scalar SDT value [0, 1]. (1 - target_sq) is high near boundaries.
        boundary_weight = 1.0 + 0.2 * (1.0 - target_sq)  # Range [1.0, 1.2]
        weighted_loss = base_loss * boundary_weight

        if mask is not None:
            # Apply the mask to the weighted loss
            masked_weighted_loss = weighted_loss * mask_sq
            # Return mean loss over masked elements
            return masked_weighted_loss.sum() / (mask.sum() + self.eps)
        else:
            # Return mean loss over all elements
            return weighted_loss.mean()


class SkeletonAwareLoss(nn.Module):
    """
    State-of-the-Art robust loss function for SkeletonAwareSpotDetector.
    Updated to produce SHARPER centroids, CLEARER flow maps, and IMPROVED segmentation.
    NOW INCLUDES BOUNDARY LOSS for enhanced sharpness.

    Key Improvements for Sharp Outputs:
    - Increased Centroid Loss Weight (w_cent).
    - Enhanced Centroid Loss with explicit peakiness encouragement.
    - Refined Flow Loss formulation (magnitude + direction).
    - NEW: Boundary Loss component for sharper edges.
    - Balanced component weights for better structural learning.
    - Maintains component loss structure and numerical stability.
    - RETAINS original QuantizedSDTLoss.
    - CRITICAL FIX: FINAL torch.clamp on total loss REMAINS REMOVED.
    """
    def __init__(self):
        """
        Initializes the SkeletonAwareLoss module with optimized component weights.
        """
        super().__init__()
        # --- CRITICAL: Optimized weights for sharper outputs ---
        self.w_sem = 1.0    # Semantic: Standard segmentation task (Kept standard)
        self.w_sdt = 1.0    # SDT: Important structural guidance (Kept standard)
        self.w_skel = 1.2   # Skeleton: Structural guidance (Slightly increased)
        self.w_cent = 1.3   # Centroid: CRUCIAL for sharp localization (Increased!)
        self.w_flow = 1.2   # Flow: Challenging vector field prediction (Kept high)
        self.w_boundary = 1 # NEW: Boundary map loss weight

        print(f"Initializing IMPROVED SkeletonAwareLoss WITH BOUNDARY LOSS with weights:")
        print(f"  SEM={self.w_sem}, SDT={self.w_sdt}, SKEL={self.w_skel}, CENT={self.w_cent}, FLOW={self.w_flow}, BOUNDARY={self.w_boundary}")

        # Initialize the original Quantized SDT loss component
        self.quantized_sdt_loss = QuantizedSDTLoss(num_bins=10, background_value=0.0)

    def focal_loss(self, pred, target, alpha=0.25, gamma=2.0, label_smoothing=0.05):
        """Focal Loss for handling class imbalance and hard examples."""
        pred_clamped = torch.clamp(pred, -10, 10)
        target_smooth = target * (1 - label_smoothing) + 0.5 * label_smoothing
        bce = F.binary_cross_entropy_with_logits(pred_clamped, target_smooth, reduction='none')
        pt = torch.exp(-bce)
        focal_weight = alpha * (1 - pt) ** gamma
        return (focal_weight * bce).mean()

    def dice_loss(self, pred, target, smooth=1e-6):
        """Dice Loss for encouraging spatial overlap."""
        pred_sig = torch.sigmoid(torch.clamp(pred, -10, 10))
        intersection = (pred_sig * target).sum(dim=(2, 3))
        union = pred_sig.sum(dim=(2, 3)) + target.sum(dim=(2, 3))
        dice = (2 * intersection + smooth) / (union + smooth)
        return 1 - dice.mean()

    def gaussian_smoothness_loss(self, pred_map, sigma=1.0, kernel_size=5):
        """Encourage smooth, Gaussian-like peaks in the centroid map."""
        pred_sig = torch.sigmoid(torch.clamp(pred_map, -10, 10))
        x = torch.arange(kernel_size, device=pred_map.device) - kernel_size // 2
        y = torch.arange(kernel_size, device=pred_map.device) - kernel_size // 2
        xx, yy = torch.meshgrid(x, y, indexing='ij')
        kernel = torch.exp(-(xx**2 + yy**2) / (2 * sigma**2))
        kernel = kernel / kernel.sum()
        kernel = kernel.view(1, 1, kernel_size, kernel_size).to(pred_map.device)

        smoothed = F.conv2d(pred_sig, kernel, padding=kernel_size//2)
        smoothness_loss = F.l1_loss(pred_sig, smoothed, reduction='mean')
        return smoothness_loss

    def skeleton_loss(self, pred, target):
        """Stable skeleton loss using BCE with logits."""
        pred_clamped = torch.clamp(pred, -10, 10)
        return F.binary_cross_entropy_with_logits(pred_clamped, target, reduction='mean')

    def flow_loss(self, pred_flow, gt_flow, mask):
        """
        IMPROVED Flow Loss: Considers both magnitude AND direction explicitly.
        Uses L1 on vectors + magnitude/direction components for robustness.
        """
        if mask.sum() < 1:
            return torch.tensor(0.0, device=pred_flow.device)

        # --- Core L1 Vector Difference ---
        diff = (pred_flow - gt_flow) * mask
        l1_diff = torch.abs(diff)
        l1_vector_loss = l1_diff.sum(dim=1, keepdim=True) # Shape: [B, 1, H, W]
        l1_loss_mean = l1_vector_loss.sum() / (mask.sum() + 1e-6)

        # --- Optional: Explicit Magnitude and Direction Penalties ---
        # This can add robustness. Weighted components.
        eps = 1e-8
        pred_mag = torch.sqrt(torch.sum(pred_flow**2, dim=1, keepdim=True) + eps)
        gt_mag = torch.sqrt(torch.sum(gt_flow**2, dim=1, keepdim=True) + eps)
        mag_loss = F.l1_loss(pred_mag * mask, gt_mag * mask, reduction='none').sum() / (mask.sum() + 1e-6)

        # Directional component (using dot product / cosine similarity)
        # Normalize vectors
        pred_norm = pred_flow / (pred_mag + eps)
        gt_norm = gt_flow / (gt_mag + eps)
        # Cosine similarity (1 - cos_theta)
        cos_sim = torch.sum(pred_norm * gt_norm, dim=1, keepdim=True)
        dir_loss = (1.0 - cos_sim) * mask
        dir_loss_mean = dir_loss.sum() / (mask.sum() + 1e-6)

        # Combine losses (example: weighted sum of L1, mag, dir)
        # L1 already includes mag/dir implicitly, but explicit terms can help.
        # Experiment with weights (e.g., 0.7*L1 + 0.2*Mag + 0.1*Dir)
        combined_loss = 0.8 * l1_loss_mean + 0.1 * mag_loss + 0.1 * dir_loss_mean

        return combined_loss

    def centroid_peakiness_loss(self, pred_cent, gt_cent, smooth=1e-6):
        """
        NEW LOSS COMPONENT: Encourage sharp peaks in centroid map.
        Rewards high values inside GT centroid regions, penalizes high values outside.
        """
        pred_sig = torch.sigmoid(torch.clamp(pred_cent, -10, 10))
        
        # --- Positive Peakiness: Reward high values inside GT centroids ---
        inside_mask = (gt_cent > 0.5).float()
        inside_sum = (pred_sig * inside_mask).sum(dim=(2, 3))
        inside_count = inside_mask.sum(dim=(2, 3)) + smooth
        # Maximize mean inside (want high values here)
        inside_mean = inside_sum / inside_count
        # Loss is negative of mean (minimizing negative mean maximizes mean)
        pos_peak_loss = -inside_mean.mean() 
        
        # --- Negative Peakiness: Penalize high values outside GT centroids ---
        outside_mask = 1.0 - inside_mask
        outside_sum = (pred_sig * outside_mask).sum(dim=(2, 3))
        outside_count = outside_mask.sum(dim=(2, 3)) + smooth
        # Minimize mean outside (want low values here)
        outside_mean = outside_sum / outside_count
        neg_peak_loss = outside_mean.mean()

        # Combine positive and negative peakiness
        # Adjust weights if needed (e.g., penalize outside more strongly)
        total_peak_loss = pos_peak_loss + neg_peak_loss 
        return total_peak_loss

    def boundary_loss(self, pred, target):
        """
        Boundary-aware loss using BCEWithLogitsLoss.
        Appropriate for binary boundary map prediction.
        """
        # Clamp logits before applying BCE
        pred_clamped = torch.clamp(pred, -10, 10)
        return F.binary_cross_entropy_with_logits(pred_clamped, target, reduction='mean')


    def forward(self, outputs, targets):
        """
        Compute the total loss and individual component losses.

        Args:
            outputs (dict): Model outputs with keys like 'sem_out', 'sdt_out', etc.
                            Shapes: (B, C, H, W)
            targets (torch.Tensor): Ground truth targets of shape [B, 7, H, W].
                                    Channels: [sem, sdt, skel, cent, flow_y, flow_x, boundary]

        Returns:
            tuple: (total_loss (torch.Tensor), losses (dict))
                   - total_loss: The final scalar tensor to call .backward() on.
                   - losses: A dictionary of individual loss components for logging.
        """
        # --- 1. Unpack Targets ---
        gt_sem = targets[:, 0:1]      # Semantic mask [B, 1, H, W]
        gt_sdt = targets[:, 1:2]      # SDT map [B, 1, H, W]
        gt_skel = targets[:, 2:3]      # Skeleton map [B, 1, H, W]
        gt_cent = targets[:, 3:4]      # Centroid heatmap [B, 1, H, W]
        gt_flow_full = targets[:, 4:6] # Flow field (Y, X) [B, 2, H, W]
        gt_boundary = targets[:, 6:7]   # Boundary map [B, 1, H, W] <-- NEW

        # --- 2. Create Semantic Mask for Spatial Weighting ---
        mask = (gt_sem > 0.5).float()  # [B, 1, H, W]

        # --- 3. Initialize Loss Dictionary ---
        losses = {}

        # --- 4. Semantic Loss ---
        sem_focal = self.focal_loss(outputs['sem_out'], gt_sem)
        sem_dice = self.dice_loss(outputs['sem_out'], gt_sem)
        losses['semantic'] = sem_focal + sem_dice

        # --- 5. SDT Loss (RETAINS ORIGINAL QUANTIZED VERSION) ---
        losses['sdt'] = self.quantized_sdt_loss(outputs['sdt_out'], gt_sdt, mask=mask)

        # --- 6. Skeleton Loss ---
        losses['skeleton'] = self.skeleton_loss(outputs['skeleton_out'], gt_skel)

        # --- 7. Centroid Loss (ENHANCED for Sharpness) ---
        cent_focal = self.focal_loss(outputs['hm_out'], gt_cent)
        cent_dice = self.dice_loss(outputs['hm_out'], gt_cent)
        cent_smooth = self.gaussian_smoothness_loss(outputs['hm_out']) * 0.02 # Slightly increased weight
        cent_peaky = self.centroid_peakiness_loss(outputs['hm_out'], gt_cent) * 0.5 # Significant weight
        losses['centroid'] = cent_focal + cent_dice + cent_smooth + cent_peaky

        # --- 8. Flow Loss (IMPROVED) ---
        losses['flow'] = self.flow_loss(outputs['flow_out'], gt_flow_full, mask)

        # --- 9. NEW: Boundary Loss ---
        losses['boundary'] = self.boundary_loss(outputs['boundary_out'], gt_boundary)

        # --- 10. CLAMP Individual Losses (Safety Net ONLY for individual components) ---
        for key in losses:
            losses[key] = torch.clamp(losses[key], 0, 5.0) # Threshold of 5.0

        # --- 11. Compute TOTAL Weighted Loss ---
        total = (
            self.w_sem * losses['semantic'] +
            self.w_sdt * losses['sdt'] +
            self.w_skel * losses['skeleton'] +
            self.w_cent * losses['centroid'] + # Includes the new peaky term
            self.w_flow * losses['flow'] +
            self.w_boundary * losses['boundary'] # <-- NEW
        )

        # --- 12. CRITICAL: FINAL CLAMP REMOVED (ENSURED) ---
        # PREVIOUS PROBLEMATIC CODE (REMOVED):
        # total = torch.clamp(total, 0, 10.0) # <<<<< MUST NOT BE HERE <<<<<
        #
        # REASON: Clamping the final scalar loss prevents true gradients from flowing.
        # This was the cause of Grad_Norm=0.00.
        #
        # The individual component clamps (step 10) protect against extreme values.
        # The weighted sum (step 11) is the natural loss that drives learning.
        # Assigning the unclamped total directly is ESSENTIAL.

        # --- 13. Return Results ---
        losses['total'] = total
        return total, losses # Return both the tensor for backward and the dict for logging

print("✅ IMPROVED SkeletonAwareLoss with Boundary Loss is ready.")

from scipy.ndimage import maximum_filter
# Cell: Optimized Tiled Inference & Instance Segmentation
def skeleton_aware_instance_segmentation(
    semantic,
    sdt_scalar,
    skeleton,
    centroid_map,
    flow,
    min_size=2,
    nms_threshold=0.3,
    flow_refine_factor=0.3
):
    """
    CORRECTED skeleton-aware instance segmentation using ALL model outputs.
    Watershed now uses -combined_map for proper direction.
    """
    H, W = semantic.shape
    
    # 1. Create robust binary mask from semantic output
    binary_mask = (semantic > 0.3).astype(np.uint8)
    
    # 2. Extract centroids with NMS (seed points)
    peaks = (centroid_map > nms_threshold) & (maximum_filter(centroid_map, size=3) == centroid_map)
    centroid_coords = np.column_stack(np.where(peaks))
    
    # 3. Create seeds for watershed - Use skeleton to refine seeds
    seeds = np.zeros_like(semantic, dtype=np.int32)
    for idx, (y, x) in enumerate(centroid_coords, 1):
        # Only use centroids that are on or near the skeleton
        if skeleton[y, x] > 0.1:  # Reduced threshold (was 0.3)
            seeds[y, x] = idx
    
    # 4. Create a SKEL-aware watershed map
    skel_mask = (skeleton > 0.5).astype(float)
    skel_ridges = 1.0 - skel_mask
    skel_influence = 0.7
    combined_map = (1.0 - skel_influence) * sdt_scalar + skel_influence * skel_ridges
    
    # 5. Use FLOW to guide the segmentation boundaries
    if flow is not None and flow.shape[0] == 2:
        flow_divergence = np.zeros_like(semantic)
        for y in range(1, H-1):
            for x in range(1, W-1):
                dx = flow[0, y, x+1] - flow[0, y, x-1]
                dy = flow[1, y+1, x] - flow[1, y-1, x]
                flow_divergence[y, x] = np.abs(dx) + np.abs(dy)
        
        flow_influence = 0.3
        combined_map = (1.0 - flow_influence) * combined_map + flow_influence * (1.0 - flow_divergence)
    
    # CRITICAL FIX: Use NEGATIVE combined map for watershed
    # Watershed flows downhill, but our SDT increases from boundary to center
    # So we need to invert the map for proper segmentation
    instance_labels = watershed(-combined_map, seeds, mask=binary_mask)
    
    # 7. Refine the segmentation using flow field
    if flow is not None and flow.shape[0] == 2:
        # For each spot, adjust based on flow
        for region_id in np.unique(instance_labels):
            if region_id == 0:  # Skip background
                continue
                
            mask = (instance_labels == region_id)
            ys, xs = np.where(mask)
            
            if len(ys) > 0:
                # Get average flow direction for this spot
                avg_flow_y = np.mean(flow[0, ys, xs])
                avg_flow_x = np.mean(flow[1, ys, xs])
                
                if np.abs(avg_flow_y) > 0.1 or np.abs(avg_flow_x) > 0.1:
                    # CRITICAL FIX: Calculate correct flow alignment
                    centroid_y, centroid_x = np.mean(ys), np.mean(xs)
                    # Vector FROM pixel TO centroid (direction we want flow to point)
                    dist_y = centroid_y - ys
                    dist_x = centroid_x - xs
                    dist_mag = np.sqrt(dist_y**2 + dist_x**2) + 1e-6
                    norm_dist_y = dist_y / dist_mag
                    norm_dist_x = dist_x / dist_mag
                    
                    # Normalize flow vector
                    flow_mag = np.sqrt(avg_flow_y**2 + avg_flow_x**2) + 1e-6
                    norm_flow_y = avg_flow_y / flow_mag
                    norm_flow_x = avg_flow_x / flow_mag
                    
                    # Dot product = cosine similarity
                    flow_alignment = norm_dist_y * norm_flow_y + norm_dist_x * norm_flow_x
                    
                    # Create refined mask
                    new_mask = np.zeros_like(mask, dtype=bool)
                    new_mask[ys, xs] = (flow_alignment > 0.5)
                    
                    # Update instance labels
                    instance_labels[new_mask] = region_id
                    instance_labels[~new_mask & (instance_labels == region_id)] = 0

    # 8. Relabel: one ID per connected component
    final_labels = np.zeros_like(instance_labels)
    regions = regionprops(instance_labels)
    new_id = 1
    filtered_spots = []

    for region in regions:
        if region.area < min_size:
            continue
        mask = (instance_labels == region.label)
        final_labels[mask] = new_id

        # 9. Refine centroid using flow
        cy, cx = region.centroid
        refined_cy, refined_cx = cy, cx
        
        if flow is not None and flow.shape[0] == 2:
            ys, xs = np.where(mask)
            if len(ys) > 0:
                avg_flow_y = flow[0, ys, xs].mean()
                avg_flow_x = flow[1, ys, xs].mean()
                refined_cy += flow_refine_factor * avg_flow_y
                refined_cx += flow_refine_factor * avg_flow_x

        # 10. Compute scores
        sdt_score = np.mean(sdt_scalar[mask])
        centroid_score = np.mean(centroid_map[mask])
        semantic_score = np.mean(semantic[mask])
        combined_score = (sdt_score + centroid_score + semantic_score) / 3.0

        filtered_spots.append({
            'y': float(refined_cy),
            'x': float(refined_cx),
            'original_y': float(cy),
            'original_x': float(cx),
            'score': float(combined_score),
            'size': int(region.area),
            'sdt_score': float(sdt_score),
            'centroid_score': float(centroid_score),
            'semantic_score': float(semantic_score)
        })
        new_id += 1

    print(f"Instance segmentation completed: Found {len(filtered_spots)} spots.")
    return final_labels, filtered_spots


def tile_inference(
    model,
    image_path,
    device='cuda',
    patch_size=256,
    overlap=0.5,
    batch_size=4,
    return_all_outputs=True
):
    """
    Fast tiled inference with full model output extraction and instance segmentation.
    Returns all outputs and final instance labels.
    """
    # 1. Load and normalize image
    image = tifffile.imread(image_path).astype(np.float32)
    H, W = image.shape
    vmin, vmax = np.percentile(image, (0.5, 99.5))
    image_norm = np.clip((image - vmin) / (vmax - vmin + 1e-8), 0, 1).astype(np.float32)

    # 2. Set model to eval mode
    model.eval()
    model.to(device)

    # 3. Calculate patch stride
    stride = int(patch_size * (1 - overlap))
    if stride <= 0:
        stride = patch_size // 2

    # 4. Initialize output accumulators
    def init_accumulator():
        return np.zeros((H, W), dtype=np.float32)

    semantic_out = init_accumulator()
    skeleton_out = init_accumulator()
    centroid_out = init_accumulator()
    sdt_probs_out = np.zeros((11, H, W), dtype=np.float32)  # 11 bins
    flow_out = np.zeros((2, H, W), dtype=np.float32)
    count_map = init_accumulator()

    # 5. Prepare patch indices
    y_coords = list(range(0, H, stride))
    x_coords = list(range(0, W, stride))
    total_patches = len(y_coords) * len(x_coords)

    # 6. Batched inference
    with torch.no_grad():
        patch_batch = []
        coords_batch = []
        pbar = tqdm(total=total_patches, desc="Tiled Inference")

        for y_start in y_coords:
            for x_start in x_coords:
                y_end = min(y_start + patch_size, H)
                x_end = min(x_start + patch_size, W)

                # Pad if needed
                pad_h = patch_size - (y_end - y_start)
                pad_w = patch_size - (x_end - x_start)
                patch = image_norm[y_start:y_end, x_start:x_end]
                patch = np.pad(patch, ((0, pad_h), (0, pad_w)), mode='reflect')

                patch_batch.append(patch)
                coords_batch.append((y_start, y_end, x_start, x_end))

                if len(patch_batch) >= batch_size or (y_start == y_coords[-1] and x_start == x_coords[-1]):
                    # Batch inference
                    patch_tensor = torch.from_numpy(np.stack(patch_batch)[..., None]).permute(0, 3, 1, 2).float().to(device)
                    with autocast('cuda'):
                        outputs = model(patch_tensor)

                    # Extract and stitch
                    for i, (y_start, y_end, x_start, x_end) in enumerate(coords_batch):
                        # Remove padding
                        h, w = y_end - y_start, x_end - x_start

                        if return_all_outputs:
                            semantic_out[y_start:y_end, x_start:x_end] += torch.sigmoid(outputs['sem_out'][i, 0]).cpu().numpy()[:h, :w]
                            skeleton_out[y_start:y_end, x_start:x_end] += torch.sigmoid(outputs['skeleton_out'][i, 0]).cpu().numpy()[:h, :w]
                            centroid_out[y_start:y_end, x_start:x_end] += torch.sigmoid(outputs['hm_out'][i, 0]).cpu().numpy()[:h, :w]
                            sdt_probs_out[:, y_start:y_end, x_start:x_end] += F.softmax(outputs['sdt_out'][i], dim=0).cpu().numpy()[:, :h, :w]
                            flow_out[:, y_start:y_end, x_start:x_end] += outputs['flow_out'][i].cpu().numpy()[:, :h, :w]

                        count_map[y_start:y_end, x_start:x_end] += 1
                    patch_batch = []
                    coords_batch = []
                    pbar.update(len(coords_batch) if not coords_batch else len(coords_batch))

        pbar.close()

    # 7. Normalize by count
    eps = 1e-8
    semantic_out /= (count_map + eps)
    skeleton_out /= (count_map + eps)
    centroid_out /= (count_map + eps)
    sdt_probs_out /= (count_map[None, ...] + eps)
    flow_out /= (count_map[None, ...] + eps)

    # 8. Reconstruct scalar SDT
    bin_centers = (np.arange(11) + 0.5) / 11
    sdt_scalar_out = np.sum(sdt_probs_out * bin_centers[:, None, None], axis=0)

    # 9. Instance Segmentation
    print("Performing instance segmentation...")
    instance_labels, filtered_spots = skeleton_aware_instance_segmentation(
        semantic=semantic_out,
        sdt_scalar=sdt_scalar_out,
        skeleton=skeleton_out,
        centroid_map=centroid_out,
        flow=flow_out,
        min_size=2,
        nms_threshold=0.3,
        flow_refine_factor=0.3
    )

    # 10. Prepare results
    results = {
        'instance_labels': instance_labels,
        'spots': filtered_spots,
        'semantic': semantic_out,
        'sdt_scalar': sdt_scalar_out,
        'skeleton': skeleton_out,
        'centroid': centroid_out,
        'flow': flow_out,
        'sdt_probs': sdt_probs_out if return_all_outputs else None
    }

    print(f"✅ Inference completed for {os.path.basename(image_path)}. Found {len(filtered_spots)} spots.")
    return results

def inspect_ground_truth_data(data_loader, device, num_batches=3, save_dir="./gt_inspection_results"):
    """
    Comprehensive inspection of the dataset's ground truth annotations.
    Validates and visualizes all target components, ensuring they are generated
    consistently and can work together for instance segmentation.
    Mirrors the component generation logic of `visualize_full_image_analysis` for validation.
    """
    import os
    import matplotlib.pyplot as plt
    import numpy as np
    from scipy.ndimage import maximum_filter
    from skimage.segmentation import watershed, find_boundaries
    from skimage.measure import regionprops
    import torch

    os.makedirs(save_dir, exist_ok=True)
    inspection_results = {
        'value_ranges': {},
        'sdt_properties': [],
        'flow_validity': [],
        'centroid_accuracy': [],
        'instance_segmentation': [], # For the simplified check within the batch
        'passed_checks': 0,
        'total_checks': 0
    }

    def check(condition, description):
        """Helper to track inspection results with clear pass/fail status"""
        inspection_results['total_checks'] += 1
        if condition:
            inspection_results['passed_checks'] += 1
            status = "✅ PASS"
        else:
            status = "❌ FAIL"
        print(f" {status}: {description}")
        return condition

    with torch.no_grad():
        for batch_idx, (images, targets) in enumerate(data_loader):
            if batch_idx >= num_batches:
                break
            images = images.float().to(device)
            targets = targets.float().to(device)
            batch_size = images.shape[0]
            print(f"\n--- Inspecting Batch {batch_idx} ---")

            # 1. Check target value ranges (with proper flow handling)
            print("1. Checking target value ranges...")
            for i in range(targets.shape[1]):
                channel_data = targets[:, i]
                min_val = channel_data.min().item()
                max_val = channel_data.max().item()

                # Special handling for flow channels (4 and 5)
                if i in [4, 5]:  # Flow Y and X
                    # Flow vectors should be in [-1, 1] (unit vectors)
                    is_valid = -1.0 <= min_val <= max_val <= 1.0
                    check(is_valid,
                          f"Flow channel {i} in [-1,1] (min: {min_val:.3f}, max: {max_val:.3f})")
                elif i == 2:  # Skeleton
                    # Skeleton can be gradient-based (0-1)
                    is_valid = 0.0 <= min_val <= max_val <= 1.0
                    check(is_valid,
                          f"Skeleton channel {i} in [0,1] (min: {min_val:.3f}, max: {max_val:.3f})")
                else:  # All other channels should be in [0, 1]
                    is_valid = 0 <= min_val <= max_val <= 1
                    check(is_valid,
                          f"Target channel {i} in [0,1] (min: {min_val:.3f}, max: {max_val:.3f})")

                inspection_results['value_ranges'].setdefault(i, []).extend([min_val, max_val])

            # 2. Check SDT properties
            print("2. Checking SDT properties...")
            for i in range(min(2, batch_size)):
                gt_sdt = targets[i, 1].cpu().numpy()  # SDT is at index 1
                gt_boundary = targets[i, 6].cpu().numpy() > 0.5  # Boundary at index 6

                # Boundary vs non-boundary comparison
                try:
                    bound_sdt_vals = gt_sdt[gt_boundary]
                    non_bound_sdt_vals = gt_sdt[~gt_boundary]

                    if len(bound_sdt_vals) > 0 and len(non_bound_sdt_vals) > 0:
                        bound_sdt_mean = bound_sdt_vals.mean()
                        non_bound_sdt_mean = non_bound_sdt_vals.mean()
                        # SDT should be higher near boundaries (value increases from center outwards)
                        is_valid = bound_sdt_mean > non_bound_sdt_mean
                        check(is_valid,
                              f"Sample {i}: SDT higher at boundary (bound: {bound_sdt_mean:.3f}, non-bound: {non_bound_sdt_mean:.3f})")
                        inspection_results['sdt_properties'].append({
                            'sample': batch_idx * batch_size + i,
                            'bound_sdt': bound_sdt_mean,
                            'non_bound_sdt': non_bound_sdt_mean
                        })
                    else:
                        print(f"  ⚠️  Insufficient pixels for boundary comparison in sample {i}")
                        inspection_results['sdt_properties'].append({
                            'sample': batch_idx * batch_size + i,
                            'bound_sdt': 0,
                            'non_bound_sdt': 0
                        })
                except Exception as e:
                    print(f"  ⚠️  Error in SDT property check for sample {i}: {e}")
                    inspection_results['sdt_properties'].append({
                        'sample': batch_idx * batch_size + i,
                        'bound_sdt': 0,
                        'non_bound_sdt': 0
                    })

            # 3. Check flow validity
            print("3. Checking flow validity...")
            # Calculate magnitude for the entire batch first, then take max
            flow_mag = torch.sqrt(targets[:, 4]**2 + targets[:, 5]**2 + 1e-8)  # Flow Y/X at indices 4/5
            max_mag = flow_mag.max().item()
            # Flow magnitude should be <= 1.0 for unit vectors
            is_valid = max_mag <= 1.0
            check(is_valid, f"Flow magnitude <= 1.0 (max: {max_mag:.3f})")
            inspection_results['flow_validity'].append(max_mag)

            # 4. Check centroid localization
            print("4. Checking centroid localization...")
            for i in range(min(2, batch_size)):
                gt_centroid = targets[i, 3].cpu().numpy()  # Centroid map at index 3
                gt_semantic = targets[i, 0].cpu().numpy() > 0.5  # Semantic at index 0

                # Centroid peaks within semantic regions
                peaks = (gt_centroid > 0.3) & (maximum_filter(gt_centroid, size=3) == gt_centroid)
                if peaks.sum() > 0:
                    centroid_in_semantic = np.logical_and(peaks, gt_semantic).sum()
                    total_centroids = peaks.sum()
                    if total_centroids > 0:
                        accuracy = centroid_in_semantic / total_centroids
                        is_valid = accuracy > 0.8 # Allow for a small percentage outside
                        check(is_valid, f"Sample {i}: Centroids within semantic ({accuracy*100:.1f}%)")
                        inspection_results['centroid_accuracy'].append(accuracy)
                    else:
                        inspection_results['centroid_accuracy'].append(0.0)
                else:
                    # No peaks found, might be an issue, but not necessarily a FAIL
                    print(f"  ⚠️  No significant centroid peaks found in sample {i}")
                    inspection_results['centroid_accuracy'].append(0.0)


            # 5. Instance Segmentation Check using GT data (Simplified per-patch check)
            # This mimics the logic of visualize_full_image_analysis but on a per-patch basis
            # for validation purposes within the batch.
            print("5. Performing simplified instance segmentation check...")
            for i in range(min(2, batch_size)):
                # Get patch targets
                gt_sem = targets[i, 0].cpu().numpy() > 0.2  # More inclusive semantic mask
                gt_sdt_scalar = targets[i, 1].cpu().numpy() # This is the scalar SDT
                gt_skel = targets[i, 2].cpu().numpy()
                gt_centroid_map = targets[i, 3].cpu().numpy()
                # gt_flow is targets[i, 4:6] - Not used in this specific check

                try:
                    # --- Replicate key logic from visualize_full_image_analysis ---

                    # 1. Create a combined binary mask (similar to full image analysis logic)
                    
                    #Expand binary mask to include skeleton regions
                    binary_mask = (gt_sem > 0.2) | (gt_skel > 0.1) | (gt_centroid > 0.2)
                    
                    # Extract centroids with NMS (seed points)
                    peaks = (gt_centroid > 0.3) & (maximum_filter(gt_centroid, size=3) == gt_centroid)
                    centroid_coords = np.column_stack(np.where(peaks))
                    
                    # Create seeds for watershed - Use centroid heatmap directly
                    seeds = np.zeros_like(gt_sem, dtype=np.int32)
                    for idx, (y, x) in enumerate(centroid_coords, 1):
                        # Use centroid heatmap directly as seeds
                        seeds[y, x] = idx
                    
                    # Alternative: Use GT SDT to refine seeds
                    # Uncomment below if needed
                    # sdt_peaks = (gt_sdt > 0.5) & (maximum_filter(gt_sdt, size=3) == gt_sdt)
                    # sdt_coords = np.column_stack(np.where(sdt_peaks))
                    # for idx, (y, x) in enumerate(sdt_coords, 1):
                    #     seeds[y, x] = idx
                    
                    # 4. Create a SKEL-aware watershed map - like full analysis
                    # Use the same combination formula and thresholds
                    skel_mask = (gt_skel > 0.5).astype(float)
                    skel_ridges = 1.0 - skel_mask
                    skel_influence = 0.7
                    combined_map = (1.0 - skel_influence) * gt_sdt + skel_influence * skel_ridges

                    # 5. Perform watershed - CRITICAL: Use NEGATIVE combined map for proper direction
                    # Watershed flows downhill, but our SDT increases from boundary to center
                    instance_labels = watershed(-combined_map, seeds, mask=binary_mask)

                    # Relabel: one ID per connected component
                    final_labels = np.zeros_like(instance_labels)
                    regions = regionprops(instance_labels)
                    num_instances = len(regions)
                    
                    check(num_instances > 0, f"Sample {i}: Instance segmentation successful ({num_instances} instances)")
                    inspection_results['instance_segmentation'].append(num_instances)
                except Exception as e:
                    print(f"  ⚠️  Instance segmentation failed for sample {i}: {e}")
                    inspection_results['instance_segmentation'].append(0)


            # 6. Visualization
            print("6. Generating visualization...")
            try:
                # Visualize the first sample of the batch
                sample_idx = 0
                fig, axes = plt.subplots(3, 5, figsize=(30, 18)) # 3 rows, 5 columns
                fig.suptitle(f'Ground Truth Inspection - Batch {batch_idx} - Sample {sample_idx}', fontsize=18)

                # --- Row 1: Core GT Components ---
                # Input Image
                axes[0, 0].imshow(images[sample_idx, 0].cpu(), cmap='gray')
                axes[0, 0].set_title('Input Image')
                axes[0, 0].axis('off')

                # Semantic Mask
                im_sem = axes[0, 1].imshow(targets[sample_idx, 0].cpu(), cmap='viridis')
                axes[0, 1].set_title('GT Semantic Mask')
                plt.colorbar(im_sem, ax=axes[0, 1])
                axes[0, 1].axis('off')

                # SDT Scalar
                im_sdt = axes[0, 2].imshow(targets[sample_idx, 1].cpu(), cmap='magma')
                axes[0, 2].set_title('GT SDT (Scalar)')
                plt.colorbar(im_sdt, ax=axes[0, 2])
                axes[0, 2].axis('off')

                # Skeleton
                im_skel = axes[0, 3].imshow(targets[sample_idx, 2].cpu(), cmap='bone')
                axes[0, 3].set_title('GT Skeleton')
                plt.colorbar(im_skel, ax=axes[0, 3])
                axes[0, 3].axis('off')

                # Centroid Heatmap
                im_cent = axes[0, 4].imshow(targets[sample_idx, 3].cpu(), cmap='hot')
                axes[0, 4].set_title('GT Centroid Heatmap')
                plt.colorbar(im_cent, ax=axes[0, 4])
                axes[0, 4].axis('off')

                # --- Row 2: Combined Views & Flow ---
                # Semantic + SDT Overlay
                axes[1, 0].imshow(targets[sample_idx, 0].cpu(), cmap='gray')
                im_sdt_ov = axes[1, 0].imshow(targets[sample_idx, 1].cpu(), cmap='magma', alpha=0.6)
                axes[1, 0].set_title('GT Sem + SDT Overlay')
                plt.colorbar(im_sdt_ov, ax=axes[1, 0])
                axes[1, 0].axis('off')

                # Semantic + Centroid Overlay
                axes[1, 1].imshow(targets[sample_idx, 0].cpu(), cmap='gray')
                im_cent_ov = axes[1, 1].imshow(targets[sample_idx, 3].cpu(), cmap='hot', alpha=0.6)
                axes[1, 1].set_title('GT Sem + Centroid Overlay')
                plt.colorbar(im_cent_ov, ax=axes[1, 1])
                axes[1, 1].axis('off')

                # Instance Segmentation Result (from the check above)
                # Re-calculate for visualization to be sure it matches
                gt_sem_viz = targets[sample_idx, 0].cpu().numpy()
                gt_sdt_scalar_viz = targets[sample_idx, 1].cpu().numpy()
                gt_skel_viz = targets[sample_idx, 2].cpu().numpy()
                gt_centroid_map_viz = targets[sample_idx, 3].cpu().numpy()

                # Replicate the check logic for visualization
                binary_mask_viz = (gt_sem_viz > 0.2) | (gt_skel_viz > 0.1)
                peaks_viz = (gt_centroid_map_viz > 0.3) & (maximum_filter(gt_centroid_map_viz, size=3) == gt_centroid_map_viz)
                centroid_coords_viz = np.column_stack(np.where(peaks_viz))
                seeds_viz = np.zeros_like(gt_sem_viz, dtype=np.int32)
                for idx, (y, x) in enumerate(centroid_coords_viz, 1):
                    if gt_skel_viz[y, x] > 0.1:
                        seeds_viz[y, x] = idx
                skel_mask_viz = (gt_skel_viz > 0.5).astype(np.float32)
                skel_ridges_viz = 1.0 - skel_mask_viz
                combined_map_viz = (1.0 - 0.7) * gt_sdt_scalar_viz + 0.7 * skel_ridges_viz
                instance_labels_viz = watershed(-combined_map_viz, seeds_viz, mask=binary_mask_viz)

                im_inst = axes[1, 2].imshow(instance_labels_viz, cmap='nipy_spectral')
                axes[1, 2].set_title('GT-Based Instance Seg (Check)')
                plt.colorbar(im_inst, ax=axes[1, 2])
                axes[1, 2].axis('off')

                # Flow Y
                im_flow_y = axes[1, 3].imshow(targets[sample_idx, 4].cpu(), cmap='RdBu_r')
                axes[1, 3].set_title('GT Flow Y')
                plt.colorbar(im_flow_y, ax=axes[1, 3])
                axes[1, 3].axis('off')

                # Flow X
                im_flow_x = axes[1, 4].imshow(targets[sample_idx, 5].cpu(), cmap='RdBu_r')
                axes[1, 4].set_title('GT Flow X')
                plt.colorbar(im_flow_x, ax=axes[1, 4])
                axes[1, 4].axis('off')

                # --- Row 3: Diagnostic Views ---
                # Flow Magnitude
                flow_mag_viz = np.hypot(targets[sample_idx, 4].cpu().numpy(), targets[sample_idx, 5].cpu().numpy())
                im_flow_mag = axes[2, 0].imshow(flow_mag_viz, cmap='plasma', vmin=0, vmax=1.0)
                axes[2, 0].set_title('GT Flow Magnitude')
                plt.colorbar(im_flow_mag, ax=axes[2, 0])
                axes[2, 0].axis('off')

                # Centroid Gradient Magnitude (Diagnostic)
                gt_centroid_np = targets[sample_idx, 3].cpu().numpy()
                gy, gx = np.gradient(gt_centroid_np)
                grad_mag_cent = np.hypot(gy, gx)
                im_cent_grad = axes[2, 1].imshow(grad_mag_cent, cmap='viridis', vmin=0, vmax=0.5)
                axes[2, 1].set_title('GT Centroid Grad Mag')
                plt.colorbar(im_cent_grad, ax=axes[2, 1])
                axes[2, 1].axis('off')

                # SDT + Skeleton Combined Map (Diagnostic)
                skel_influence_diag = 0.7
                combined_map_diag = (1.0 - skel_influence_diag) * targets[sample_idx, 1].cpu().numpy() + skel_influence_diag * (1.0 - targets[sample_idx, 2].cpu().numpy())
                im_comb_diag = axes[2, 2].imshow(combined_map_diag, cmap='viridis')
                axes[2, 2].set_title('GT SDT + Skel Comb Map')
                plt.colorbar(im_comb_diag, ax=axes[2, 2])
                axes[2, 2].axis('off')

                # Valid Seeds on Skeleton (Diagnostic)
                seeds_diag_viz = np.zeros_like(gt_sem_viz, dtype=np.float32) # Use float for imshow
                for idx, (y, x) in enumerate(centroid_coords_viz, 1):
                    if targets[sample_idx, 2].cpu().numpy()[y, x] > 0.1:
                        seeds_diag_viz[y, x] = 1.0 # Mark presence
                im_seeds_diag = axes[2, 3].imshow(seeds_diag_viz, cmap='hot')
                axes[2, 3].set_title('GT Valid Seeds (Sk>0.1)')
                plt.colorbar(im_seeds_diag, ax=axes[2, 3])
                axes[2, 3].axis('off')

                # Instance Boundaries (Diagnostic)
                try:
                    boundaries_diag = find_boundaries(instance_labels_viz, connectivity=1)
                    im_bound_diag = axes[2, 4].imshow(boundaries_diag, cmap='gray')
                    axes[2, 4].set_title('GT Instance Boundaries')
                    plt.colorbar(im_bound_diag, ax=axes[2, 4])
                except Exception as e:
                    axes[2, 4].text(0.5, 0.5, f'Boundary Viz Error:\n{str(e)[:20]}', ha='center', va='center', transform=axes[2, 4].transAxes, fontsize=8)
                    axes[2, 4].set_title('GT Instance Boundaries')
                axes[2, 4].axis('off')


                plt.tight_layout(rect=[0, 0, 1, 0.96]) # Make room for suptitle
                plot_filename = os.path.join(save_dir, f"gt_inspection_batch_{batch_idx}_sample_{sample_idx}.png")
                plt.savefig(plot_filename, dpi=150, bbox_inches='tight')
                plt.close(fig) # Explicitly close the figure
                print(f"  📊 GT inspection plot saved to {plot_filename}")

            except Exception as e:
                print(f"  ⚠️  Visualization error for batch {batch_idx}: {e}")
                import traceback
                traceback.print_exc() # Print full traceback for debugging

    # --- Summary ---
    print("\n" + "="*60)
    print("GROUND TRUTH INSPECTION SUMMARY")
    print("="*60)
    print(f"Passed checks: {inspection_results['passed_checks']}/{inspection_results['total_checks']}")
    if inspection_results['flow_validity']:
        print(f"Max flow magnitude: {max(inspection_results['flow_validity']):.3f}")
    if inspection_results['centroid_accuracy']:
        avg_acc = np.mean(inspection_results['centroid_accuracy'])
        print(f"Average centroid accuracy: {avg_acc*100:.1f}%")
    if inspection_results['instance_segmentation']:
        avg_inst = np.mean(inspection_results['instance_segmentation'])
        print(f"Avg instances per sample (simplified check): {avg_inst:.1f}")

    success_rate = inspection_results['passed_checks'] / inspection_results['total_checks'] if inspection_results['total_checks'] > 0 else 0
    if success_rate >= 0.85: # Slightly lower threshold for a "pass"
        print("🎉 INSPECTION INDICATES: Ground truth data is likely consistent and correct.")
        print("   The components generated by the dataset loader align with the expected structure.")
        print("   Instance segmentation logic (simplified check) is functional.")
    else:
        print("⚠️ INSPECTION INDICATES: Potential issues in ground truth data.")
        print("   Please review the dataset loader implementation and the generated targets.")

    # Save detailed results (excluding large arrays like 'value_ranges')
    import json
    # Create a copy of results and remove large items
    results_summary = {}
    for key, value in inspection_results.items():
        if key != 'value_ranges': # Exclude raw value ranges
            results_summary[key] = value
        else:
            # Optionally, summarize value_ranges
            summarized_ranges = {}
            for ch, vals in value.items():
                if vals:
                    summarized_ranges[ch] = {'min': min(vals), 'max': max(vals), 'count': len(vals)}
            results_summary['value_ranges_summary'] = summarized_ranges

    try:
        summary_filename = os.path.join(save_dir, "gt_inspection_summary.json")
        with open(summary_filename, 'w') as f:
            json.dump(results_summary, f, indent=2)
        print(f"📋 Summary of inspection results saved to {summary_filename}")
    except Exception as e:
        print(f"⚠️ Error saving JSON summary: {e}")

    return inspection_results

print("✅ inspect_ground_truth_data function is ready for use.")
print("   It now incorporates logic consistent with visualize_full_image_analysis for validation.")

# Example usage (uncomment to run):
# Assuming you have data_loader, device defined
# results = inspect_ground_truth_data(data_loader, device, num_batches=2, save_dir="./gt_inspection_output_final")




from skimage.segmentation import watershed
# Cell 8: Updated Inspection Function (Corrected)
def inspect_skeleton_aware_model(model, data_loader, device, num_batches=3, save_dir="./model_inspection_results"):
    """
    Comprehensive inspection of the SkeletonAwareSpotDetector model outputs.
    Now includes REAL instance segmentation validation using all components.
    """
    import os
    import matplotlib.pyplot as plt
    import numpy as np
    from scipy.ndimage import maximum_filter
    from skimage.segmentation import watershed
    from skimage.measure import label
    import torch
    import torch.nn.functional as F
    
    try:
        from torch.amp import autocast  # PyTorch 2.0+
    except ImportError:
        from torch.cuda.amp import autocast  # PyTorch < 2.0

    model.eval()
    os.makedirs(save_dir, exist_ok=True)
    inspection_results = {
        'output_shapes': {},
        'value_ranges': {},
        'sdt_properties': [],
        'flow_validity': [],
        'centroid_accuracy': [],
        'instance_segmentation': [],
        'passed_checks': 0,
        'total_checks': 0
    }

    def check(condition, description):
        """Helper to track inspection results"""
        inspection_results['total_checks'] += 1
        if condition:
            inspection_results['passed_checks'] += 1
            status = "✅ PASS"
        else:
            status = "❌ FAIL"
        print(f" {status}: {description}")
        return condition

    with torch.no_grad():
        for batch_idx, (images, targets) in enumerate(data_loader):
            if batch_idx >= num_batches:
                break
            images = images.float().to(device)
            targets = targets.float().to(device)
            
            with autocast('cuda'):
                outputs = model(images)
            batch_size = images.shape[0]

            # 1. Check output shapes
            if batch_idx == 0:
                print("1. Checking output shapes...")
                for name, tensor in outputs.items():
                    inspection_results['output_shapes'][name] = list(tensor.shape)
                    check(len(tensor.shape) == 4, f"{name} has 4 dimensions (B,C,H,W)")
                
                # Channel checks
                check(outputs['sem_out'].shape[1] == 1, "Semantic output has 1 channel")
                check(outputs['sdt_out'].shape[1] == 11, "SDT output has 11 bins")
                check(outputs['skeleton_out'].shape[1] == 1, "Skeleton output has 1 channel")
                check(outputs['hm_out'].shape[1] == 1, "Centroid map has 1 channel")
                check(outputs['flow_out'].shape[1] == 2, "Flow output has 2 channels (y,x)")

            # 2. Check value ranges
            print("2. Checking value ranges...")
            for name, tensor in outputs.items():
                if name == 'sdt_out':
                    # SDT logits can be any value
                    continue
                elif name == 'flow_out':
                    # Check flow magnitude constraint
                    flow_mag = torch.sqrt(torch.sum(tensor**2, dim=1, keepdim=True) + 1e-8)
                    max_mag = flow_mag.max().item()
                    check(max_mag <= 1, f"Flow magnitude <= 1.5 (max: {max_mag:.3f})")
                    inspection_results['flow_validity'].append(max_mag)
                else:
                    min_val = tensor.min().item()
                    max_val = tensor.max().item()
                    check(0 <= min_val <= max_val <= 1, f"{name} in [0,1] (min: {min_val:.3f}, max: {max_val:.3f})")
                    inspection_results['value_ranges'].setdefault(name, []).extend([min_val, max_val])

            # 3. Check SDT properties
            print("3. Checking SDT properties...")
            sdt_probs = F.softmax(outputs['sdt_out'], dim=1)
            bin_centers = (torch.arange(0, sdt_probs.shape[1], dtype=torch.float32, device=sdt_probs.device) + 0.5) / sdt_probs.shape[1]
            bin_centers = bin_centers.view(1, -1, 1, 1)
            sdt_recon = torch.sum(sdt_probs * bin_centers, dim=1)

            for i in range(min(2, batch_size)):
                pred_sdt = sdt_recon[i].cpu().numpy().astype(np.float32)
                gt_sdt = targets[i, 1].cpu().numpy()  # Assuming SDT target is at index 1
                
                # Boundary vs non-boundary comparison
                try:
                    boundary_mask = (targets[i, 6].cpu().numpy() > 0.5).astype(bool)  # Assuming boundary is at index 6
                    bound_sdt_vals = pred_sdt[boundary_mask]
                    non_bound_sdt_vals = pred_sdt[~boundary_mask]
                    
                    bound_sdt_mean = bound_sdt_vals.mean().item()
                    non_bound_sdt_mean = non_bound_sdt_vals.mean().item()
                    check(bound_sdt_mean > non_bound_sdt_mean, 
                        f"Sample {i}: SDT higher at boundary (bound: {bound_sdt_mean:.3f}, non-bound: {non_bound_sdt_mean:.3f})")
                    inspection_results['sdt_properties'].append({
                        'sample': batch_idx*batch_size + i,
                        'bound_sdt': bound_sdt_mean,
                        'non_bound_sdt': non_bound_sdt_mean
                    })
                except Exception as e:
                    print(f" Warning: Insufficient pixels for boundary/non-boundary comparison in sample {i}.")
                    inspection_results['sdt_properties'].append({
                        'sample': batch_idx*batch_size + i,
                        'bound_sdt': 0,
                        'non_bound_sdt': 0
                    })

            # 4. Check centroid localization and smoothness
            print("4. Checking centroid localization and smoothness...")
            for i in range(min(2, batch_size)):
                pred_hm = torch.sigmoid(outputs['hm_out'][i, 0]).cpu().numpy().astype(np.float32)
                pred_semantic = torch.sigmoid(outputs['sem_out'][i, 0]).cpu().numpy() > 0.5
                
                # Centroid within semantic
                peaks = (pred_hm > 0.3) & (maximum_filter(pred_hm, size=3) == pred_hm)
                if peaks.sum() > 0:
                    centroid_in_semantic = np.logical_and(peaks, pred_semantic).sum()
                    total_centroids = peaks.sum()
                    accuracy = centroid_in_semantic / total_centroids
                    check(accuracy > 0.8, f"Sample {i}: Centroids within semantic ({accuracy*100:.1f}%)")
                    inspection_results['centroid_accuracy'].append(accuracy)
                else:
                    inspection_results['centroid_accuracy'].append(0.0)
                
                # Smoothness check
                grad_y, grad_x = np.gradient(pred_hm)
                grad_mag = np.hypot(grad_y, grad_x)
                max_grad = grad_mag.max()
                check(max_grad < 0.3, f"Sample {i}: Centroid heatmap smooth (max grad: {max_grad:.3f})")

            # 5. REAL INSTANCE SEGMENTATION TEST
            print("5. Performing REAL instance segmentation test...")
            for i in range(min(2, batch_size)):
                # Extract model outputs
                pred_sem = torch.sigmoid(outputs['sem_out'][i, 0]).cpu().numpy().astype(np.float32)
                pred_sdt = sdt_recon[i].cpu().numpy().astype(np.float32)
                pred_skel = torch.sigmoid(outputs['skeleton_out'][i, 0]).cpu().numpy().astype(np.float32)
                pred_cent = torch.sigmoid(outputs['hm_out'][i, 0]).cpu().numpy().astype(np.float32)
                pred_flow = outputs['flow_out'][i].cpu().numpy().astype(np.float32)
                
                try:
                    # Perform real instance segmentation
                    final_labels, filtered_spots = skeleton_aware_instance_segmentation(
                        semantic=pred_sem,
                        sdt_scalar=pred_sdt,
                        skeleton=pred_skel,
                        centroid_map=pred_cent,
                        flow=pred_flow,
                        min_size=5,
                        nms_threshold=0.4,
                        flow_refine_factor=0.3
                    )
                    
                    num_instances = len(filtered_spots)
                    check(num_instances > 0, f"Sample {i}: Real instance segmentation successful ({num_instances} instances)")
                    inspection_results['instance_segmentation'].append(num_instances)
                except Exception as e:
                    print(f" Instance segmentation failed for sample {i}: {e}")
                    inspection_results['instance_segmentation'].append(0)

            # 6. Visualization
            print("6. Generating visualization...")
            try:
                fig, axes = plt.subplots(3, 5, figsize=(25, 15))
                sample_idx = 0  # Visualize first sample of the batch
                
                # Input and Main Outputs
                axes[0,0].imshow(images[sample_idx, 0].cpu(), cmap='gray')
                axes[0,0].set_title('Input Image')
                axes[0,0].axis('off')
                
                axes[0,1].imshow(torch.sigmoid(outputs['sem_out'][sample_idx, 0]).cpu(), cmap='viridis')
                axes[0,1].set_title('Semantic Mask')
                axes[0,1].axis('off')
                
                im2 = axes[0,2].imshow(sdt_recon[sample_idx].cpu(), cmap='magma')
                axes[0,2].set_title('SDT (Reconstructed)')
                plt.colorbar(im2, ax=axes[0,2])
                
                axes[0,3].imshow(torch.sigmoid(outputs['skeleton_out'][sample_idx, 0]).cpu(), cmap='bone')
                axes[0,3].set_title('Skeleton')
                axes[0,3].axis('off')
                
                axes[0,4].imshow(torch.sigmoid(outputs['hm_out'][sample_idx, 0]).cpu(), cmap='hot')
                axes[0,4].set_title('Centroid Heatmap')
                axes[0,4].axis('off')
                
                # Combined Visualizations
                axes[1,0].imshow(torch.sigmoid(outputs['sem_out'][sample_idx, 0]).cpu(), cmap='gray')
                axes[1,0].imshow(sdt_recon[sample_idx].cpu(), cmap='magma', alpha=0.6)
                axes[1,0].set_title('Sem + SDT')
                axes[1,0].axis('off')
                
                axes[1,1].imshow(torch.sigmoid(outputs['sem_out'][sample_idx, 0]).cpu(), cmap='gray')
                axes[1,1].imshow(torch.sigmoid(outputs['hm_out'][sample_idx, 0]).cpu(), cmap='hot', alpha=0.6)
                axes[1,1].set_title('Sem + Centroid')
                axes[1,1].axis('off')
                
                # Show REAL instance segmentation result
                axes[1,2].imshow(final_labels, cmap='nipy_spectral')
                axes[1,2].set_title('Real Instance Segmentation')
                axes[1,2].axis('off')
                
                axes[1,3].imshow(pred_flow[sample_idx, 0], cmap='RdBu_r')
                axes[1,3].set_title('Flow Y')
                axes[1,3].axis('off')
                
                axes[1,4].imshow(pred_flow[sample_idx, 1], cmap='RdBu_r')
                axes[1,4].set_title('Flow X')
                axes[1,4].axis('off')
                
                # Diagnostic Visualizations
                grad_mag_vis = np.hypot(
                    pred_flow[sample_idx, 0],
                    pred_flow[sample_idx, 1]
                )
                im = axes[2,0].imshow(grad_mag_vis, cmap='viridis', vmin=0, vmax=0.3)
                plt.colorbar(im, ax=axes[2,0])
                axes[2,0].set_title('Flow Magnitude')
                axes[2,0].axis('off')
                
                axes[2,1].axis('off')  # Empty space
                
                plt.tight_layout()
                plt.suptitle(f'Model Inspection - Batch {batch_idx}', fontsize=16)
                plt.subplots_adjust(top=0.93)
                plt.savefig(os.path.join(save_dir, f"model_inspection_batch_{batch_idx}.png"), dpi=150, bbox_inches='tight')
                plt.close()
            except Exception as e:
                print(f" Visualization error: {e}")

    # Summary
    print("\n" + "="*60)
    print("MODEL INSPECTION SUMMARY")
    print("="*60)
    print(f"Passed checks: {inspection_results['passed_checks']}/{inspection_results['total_checks']}")
    if inspection_results['flow_validity']:
        print(f"Max flow magnitude: {max(inspection_results['flow_validity']):.3f}")
    if inspection_results['centroid_accuracy']:
        avg_acc = np.mean(inspection_results['centroid_accuracy'])
        print(f"Average centroid accuracy: {avg_acc*100:.1f}%")
    if inspection_results['instance_segmentation']:
        avg_inst = np.mean(inspection_results['instance_segmentation'])
        print(f"Avg instances per sample: {avg_inst:.1f}")
    
    success_rate = inspection_results['passed_checks'] / inspection_results['total_checks'] if inspection_results['total_checks'] > 0 else 0
    if success_rate >= 0.9:
        print("🎉 INSPECTION PASSED: Model outputs are consistent and correct.")
    else:
        print("⚠️ INSPECTION FAILED: Some checks did not pass. Please review.")
    
    return inspection_results

# Example usage (uncomment to run):
# device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
# model = SkeletonAwareSpotDetector(in_ch=1, base_ch=48).to(device)
# # Load a trained model if available
# # checkpoint = torch.load('path/to/best_model.pth')
# # model.load_state_dict(checkpoint['model_state_dict'])
# 
# # Create data loaders (replace with your actual paths)
# # train_loader, val_loader = create_skeleton_aware_data_loaders(image_paths, mask_paths)
# 
# # Run inspection
# # results = inspect_skeleton_aware_model(model, val_loader, device, num_batches=2)

print("inspect_skeleton_aware_model function updated with fixes.")

# Cell: State-of-the-Art Improved Training Function with Stability Fixes and Visualization
import os
import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
import torch.nn.functional as F

# - AMP import: compatible with both PyTorch 1.x and 2.x -
try:
    from torch.amp import GradScaler, autocast  # PyTorch 2.0+
except ImportError:
    from torch.cuda.amp import GradScaler, autocast  # PyTorch < 2.0

def visualize_training_progress(model, data_loader, device, save_dir, epoch,
                                 train_losses=None, val_losses=None,
                                 component_train_losses=None, component_val_losses=None):
    """
    Visualization showing all model outputs and PROPER instance segmentation
    that uses all components synergistically.
    """
    model.eval()
    os.makedirs(save_dir, exist_ok=True)
    with torch.no_grad():
        images, targets = next(iter(data_loader))
        images = images.float().to(device)
        with autocast(device_type=device.type):
            outputs = model(images)
        
        # Reconstruct SDT scalar from 11 bins (matching inference)
        sdt_probs = F.softmax(outputs['sdt_out'], dim=1)
        bin_centers = (torch.arange(0, sdt_probs.shape[1], dtype=torch.float32, 
                                 device=sdt_probs.device) + 0.5) / sdt_probs.shape[1]
        bin_centers = bin_centers.view(1, -1, 1, 1)
        sdt_scalar = torch.sum(sdt_probs * bin_centers, dim=1)

    # Process first sample
    i = 0
    # Convert to float32 for SciPy compatibility
    pred_sem = torch.sigmoid(outputs['sem_out'][i, 0]).cpu().numpy().astype(np.float32)
    pred_sdt = sdt_scalar[i].cpu().numpy().astype(np.float32)
    pred_cent = torch.sigmoid(outputs['hm_out'][i, 0]).cpu().numpy().astype(np.float32)
    pred_skel = torch.sigmoid(outputs['skeleton_out'][i, 0]).cpu().numpy().astype(np.float32)
    pred_flow = outputs['flow_out'][i].cpu().numpy().astype(np.float32)

    # Instance Segmentation (using the PROPER method)
    binary_mask = (pred_sem > 0.3).astype(np.uint8)
    peaks = (pred_cent > 0.3) & (maximum_filter(pred_cent, size=3) == pred_cent)
    centroid_coords = np.column_stack(np.where(peaks))
    seeds = np.zeros_like(pred_sem, dtype=np.int32)
    for idx, (y, x) in enumerate(centroid_coords, 1):
        # CRITICAL FIX: Use lower skeleton threshold (0.1 instead of 0.3)
        if pred_skel[y, x] > 0.1:  # Skeleton strength threshold
            seeds[y, x] = idx
    
    # Create a SKEL-aware watershed map (the CORE innovation)
    skel_mask = (pred_skel > 0.5).astype(float)
    # Skeleton should create "ridges" (high values) between spots
    skel_ridges = 1.0 - skel_mask
    
    # Enhance SDT with skeleton information
    # Where skeleton is strong, create stronger ridges
    skel_influence = 0.7  # How much skeleton affects the watershed
    combined_map = (1.0 - skel_influence) * pred_sdt + skel_influence * skel_ridges
    
    # Use FLOW to guide the segmentation boundaries
    if pred_flow is not None and pred_flow.shape[0] == 2:
        # Create flow-based boundary map
        flow_magnitude = np.sqrt(pred_flow[0]**2 + pred_flow[1]**2)
        # Flow vectors should point toward spot centers, so boundaries form where flow changes direction
        flow_divergence = np.zeros_like(pred_sem)
        for y in range(1, pred_sem.shape[0]-1):
            for x in range(1, pred_sem.shape[1]-1):
                # Calculate divergence (where flow vectors change direction)
                dx = pred_flow[0, y, x+1] - pred_flow[0, y, x-1]
                dy = pred_flow[1, y+1, x] - pred_flow[1, y-1, x]
                flow_divergence[y, x] = np.abs(dx) + np.abs(dy)
        
        # Enhance boundaries where flow changes direction
        flow_influence = 0.3
        combined_map = (1.0 - flow_influence) * combined_map + flow_influence * (1.0 - flow_divergence)
    
    # Watershed using the SKEL-aware map (CRITICAL: Use NEGATIVE combined map)
    instance_labels = watershed(-combined_map, seeds, mask=binary_mask)

    # Visualization
    fig, axes = plt.subplots(3, 5, figsize=(30, 18))
    fig.suptitle(f'Training Progress - Epoch {epoch+1}', fontsize=16)

    # Row 1: Inputs and Core Outputs
    axes[0, 0].imshow(images[i, 0].cpu(), cmap='gray')
    axes[0, 0].set_title('Input Image')
    axes[0, 0].axis('off')

    axes[0, 1].imshow(pred_sem, cmap='viridis')
    axes[0, 1].set_title('Semantic Mask')
    axes[0, 1].axis('off')

    im2 = axes[0, 2].imshow(pred_sdt, cmap='magma')
    axes[0, 2].set_title('SDT (Reconstructed)')
    plt.colorbar(im2, ax=axes[0, 2])

    axes[0, 3].imshow(pred_skel, cmap='bone')
    axes[0, 3].set_title('Skeleton')
    axes[0, 3].axis('off')

    axes[0, 4].imshow(pred_cent, cmap='hot')
    axes[0, 4].set_title('Centroid Heatmap')
    axes[0, 4].axis('off')

    # Row 2: Combined Visualizations
    axes[1, 0].imshow(pred_sem, cmap='gray')
    axes[1, 0].imshow(pred_sdt, cmap='magma', alpha=0.6)
    axes[1, 0].set_title('Sem + SDT')
    axes[1, 0].axis('off')

    axes[1, 1].imshow(pred_sem, cmap='gray')
    axes[1, 1].imshow(pred_cent, cmap='hot', alpha=0.6)
    axes[1, 1].set_title('Sem + Centroid')
    axes[1, 1].axis('off')

    axes[1, 2].imshow(instance_labels, cmap='nipy_spectral')
    axes[1, 2].set_title('Instance Segmentation')
    axes[1, 2].axis('off')

    axes[1, 3].imshow(pred_flow[0], cmap='RdBu_r')
    axes[1, 3].set_title('Flow Y')
    axes[1, 3].axis('off')

    axes[1, 4].imshow(pred_flow[1], cmap='RdBu_r')
    axes[1, 4].set_title('Flow X')
    axes[1, 4].axis('off')

    # Row 3: Diagnostic Visualizations
    # Show how skeleton enhances SDT
    skel_mask = (pred_skel > 0.5).astype(float)
    skel_ridges = 1.0 - skel_mask
    combined_map = 0.3 * pred_sdt + 0.7 * skel_ridges
    axes[2, 0].imshow(combined_map, cmap='viridis')
    axes[2, 0].set_title('SDT + Skeleton Combined')
    axes[2, 0].axis('off')
    
    # Show flow divergence
    if pred_flow is not None and pred_flow.shape[0] == 2:
        flow_magnitude = np.sqrt(pred_flow[0]**2 + pred_flow[1]**2)
        flow_divergence = np.zeros_like(pred_sem)
        for y in range(1, pred_sem.shape[0]-1):
            for x in range(1, pred_sem.shape[1]-1):
                dx = pred_flow[0, y, x+1] - pred_flow[0, y, x-1]
                dy = pred_flow[1, y+1, x] - pred_flow[1, y-1, x]
                flow_divergence[y, x] = np.abs(dx) + np.abs(dy)
        axes[2, 1].imshow(flow_divergence, cmap='hot')
        axes[2, 1].set_title('Flow Divergence')
        axes[2, 1].axis('off')
    
    # Show seeds used for watershed
    peaks = (pred_cent > 0.4) & (maximum_filter(pred_cent, size=3) == pred_cent)
    seeds = np.zeros_like(pred_sem, dtype=np.int32)
    for idx, (y, x) in enumerate(np.column_stack(np.where(peaks)), 1):
        # CRITICAL FIX: Use lower skeleton threshold (0.1 instead of 0.3)
        if pred_skel[y, x] > 0.1:  # Only use centroids on skeleton
            seeds[y, x] = idx
    axes[2, 2].imshow(seeds > 0, cmap='hot')
    axes[2, 2].set_title('Valid Seeds (on skeleton)')
    axes[2, 2].axis('off')
    
    # Show final instance boundaries
    boundaries = find_boundaries(instance_labels, connectivity=1)
    axes[2, 3].imshow(boundaries, cmap='gray')
    axes[2, 3].set_title('Instance Boundaries')
    axes[2, 3].axis('off')
    
    # --- 4. Plot Loss Curves (if data is provided) ---
    if train_losses and val_losses:
        axes[2, 4].clear() # Clear the axis for the plot
        epochs_plotted = range(1, len(train_losses) + 1)
        axes[2, 4].plot(epochs_plotted, train_losses, label='Train Loss')
        axes[2, 4].plot(epochs_plotted, val_losses, label='Val Loss')
        axes[2, 4].set_xlabel('Epoch')
        axes[2, 4].set_ylabel('Loss')
        axes[2, 4].set_title('Overall Loss')
        axes[2, 4].legend()
        axes[2, 4].grid(True)
    else:
        axes[2, 4].axis('off') # Keep empty if no data

    plt.tight_layout()
    progress_plot_path = os.path.join(save_dir, f'progress_epoch_{epoch+1}.png')
    plt.savefig(progress_plot_path, dpi=150, bbox_inches='tight')
    plt.close()
    print(f"📊 Progress plot saved to {progress_plot_path}")


def train_skeleton_aware_model_sota(model, train_loader, val_loader, num_epochs=300,
                                    device='cuda', model_dir='./',
                                    resume=True, early_stopping_patience=30):
    """
    State-of-the-Art optimized training loop for SkeletonAwareSpotDetector.
    Key improvements:
    - Robust resume capability with full state saving/loading.
    - Early stopping to prevent overfitting and save time.
    - Improved optimizer (AdamW) with potential fused implementation.
    - Better LR scheduler (CosineAnnealingWarmRestarts).
    - Enhanced gradient clipping.
    - Comprehensive logging and visualization.
    - Dynamic curriculum learning via loss function weight updates.
    """
    os.makedirs(model_dir, exist_ok=True)
    print(f"Model outputs will be saved to: {model_dir}")

    # --- 1. Loss Function ---
    criterion = SkeletonAwareLoss()
    print("Using loss function: SkeletonAwareLoss")

    # --- 2. Optimizer Setup (Fixed) ---
    base_lr = 5e-4 # Reduced for stability with improved loss/grad clipping
    optimizer_kwargs = {
        'lr': base_lr,
        'weight_decay': 1e-2,
        'betas': (0.9, 0.999)
    }
    # Direct fused AdamW creation
    try:
        optimizer = torch.optim.AdamW(model.parameters(), fused=True, **optimizer_kwargs)
        print("✅ Using fused AdamW optimizer.")
    except Exception as e:
        optimizer = torch.optim.AdamW(model.parameters(), **optimizer_kwargs)
        print(f"⚠️ Fused AdamW failed ({e}), using standard AdamW.")

    # --- 3. Better Learning Rate Schedule ---
    scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer,
        T_0=20,           # Restart every 20 epochs
        T_mult=1,         # Keep same cycle length
        eta_min=1e-6      # Lower minimum LR
    )
    print("Using LR Scheduler: CosineAnnealingWarmRestarts")

    # --- 4. AMP Scaler ---
    scaler = GradScaler()
    print("Using Automatic Mixed Precision (AMP).")

    # --- 5. Model & Device ---
    model.to(device)
    print(f"Model moved to device: {device}")

    # --- 6. State for Resuming and Early Stopping ---
    start_epoch = 0
    best_loss = float('inf')
    epochs_since_improvement = 0

    # Initialize loss tracking
    initial_train_losses, initial_val_losses = [], []
    initial_component_train_losses = {
        'semantic': [], 'sdt': [], 'skeleton': [], 'centroid': [], 'flow': [], 'boundary': []
    }
    initial_component_val_losses = {
        'semantic': [], 'sdt': [], 'skeleton': [], 'centroid': [], 'flow': [], 'boundary': []
    }

    # --- 7. RESUME LOGIC ---
    best_model_path = os.path.join(model_dir, 'best_model.pth')
    final_model_path = os.path.join(model_dir, 'final_model.pth')

    if resume and os.path.isfile(best_model_path):
        print(f"Attempting to resume training from {best_model_path}...")
        try:
            checkpoint = torch.load(best_model_path, map_location=device, weights_only=False)
            model.load_state_dict(checkpoint['model_state_dict'])
            print("✅ Model state loaded successfully.")

            if 'optimizer_state_dict' in checkpoint:
                optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
                print("✅ Optimizer state loaded successfully.")
            else:
                print("⚠️ Warning: 'optimizer_state_dict' not found in checkpoint. Starting optimizer from scratch.")

            if 'scheduler_state_dict' in checkpoint:
                try:
                    scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
                    print("✅ Scheduler state loaded successfully.")
                except Exception as e:
                    print(f"⚠️ Warning: Failed to load scheduler state: {e}. Scheduler will restart.")
            else:
                print("⚠️ Warning: 'scheduler_state_dict' not found in checkpoint. Scheduler will restart.")

            start_epoch = checkpoint.get('epoch', 0) + 1
            best_loss = checkpoint.get('loss', float('inf'))
            epochs_since_improvement = checkpoint.get('epochs_since_improvement', 0)

            if 'train_losses' in checkpoint:
                initial_train_losses = checkpoint['train_losses'][:start_epoch]
            if 'val_losses' in checkpoint:
                initial_val_losses = checkpoint['val_losses'][:start_epoch]
            if 'component_train_losses' in checkpoint:
                for k in initial_component_train_losses:
                    if k in checkpoint['component_train_losses']:
                        initial_component_train_losses[k] = checkpoint['component_train_losses'][k][:start_epoch]
            if 'component_val_losses' in checkpoint:
                for k in initial_component_val_losses:
                    if k in checkpoint['component_val_losses']:
                        initial_component_val_losses[k] = checkpoint['component_val_losses'][k][:start_epoch]

            print(f"✅ Resumed from epoch {start_epoch}, best val loss: {best_loss:.6f}, "
                  f"epochs since improvement: {epochs_since_improvement}")
        except Exception as e:
            print(f"❌ Failed to resume: {e}. Starting from scratch.")
            start_epoch = 0
            best_loss = float('inf')
            epochs_since_improvement = 0
    else:
        if resume:
            print(f"No checkpoint found at {best_model_path}. Starting from scratch.")
        else:
            print("Resume disabled. Starting from scratch.")

    # --- 8. Initialize loss tracking ---
    train_losses = initial_train_losses[:]
    val_losses = initial_val_losses[:]
    component_train_losses = {k: v[:] for k, v in initial_component_train_losses.items()}
    component_val_losses = {k: v[:] for k, v in initial_component_val_losses.items()}

    # --- 9. Training Loop ---
    print(f"\n--- Starting/Resuming Training from epoch {start_epoch + 1} to {num_epochs} ---")

    for epoch in range(start_epoch, num_epochs):
        # --- Curriculum Learning: Update epoch for loss ---
        if hasattr(criterion, 'set_epoch'):
            criterion.set_epoch(epoch)

        # --- Training Phase ---
        model.train()
        epoch_train_losses = []
        epoch_train_components = {k: 0.0 for k in component_train_losses}

        train_pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs} [Train]")
        for images, targets in train_pbar:
            images = images.float().to(device, non_blocking=True)
            targets = targets.float().to(device, non_blocking=True)

            optimizer.zero_grad(set_to_none=True)

            with autocast(device_type=device.type):
                outputs = model(images)
                # --- CRITICAL FIX: Explicitly unpack the returned tuple ---
                # Ensures 'loss' is the first tensor (unclamped total) for .backward()
                # and 'loss_components' is the dictionary for logging/monitoring.
                loss, loss_components = criterion(outputs, targets)
                # ----------------------------------------------------------

            if not torch.isnan(loss) and not torch.isinf(loss):
                scaler.scale(loss).backward() # <<-- Use the 'loss' tensor directly
                # --- CRITICAL FIX: Increased gradient clipping threshold for stability ---
                scaler.unscale_(optimizer)
                grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=5.0) # <<< KEY CHANGE >>>
                scaler.step(optimizer)
                scaler.update()
                scheduler.step() # Consider if T_0=1 is intended

                epoch_train_losses.append(loss.item())
                for k in epoch_train_components:
                    if k in loss_components: # Use the unpacked 'loss_components' dict
                        epoch_train_components[k] += loss_components[k].item()

                if len(epoch_train_losses) % 10 == 0 or len(epoch_train_losses) == len(train_loader):
                    current_lr = optimizer.param_groups[0]['lr']
                    train_pbar.set_postfix({
                        'Loss': f'{loss.item():.4f}',
                        'LR': f'{current_lr:.2e}',
                        'Grad_Norm': f'{grad_norm:.2f}'
                    })
            else:
                print(f"⚠️  Skipping batch due to invalid loss (NaN/Inf): {loss.item()}")
                optimizer.zero_grad(set_to_none=True) # Ensure no stale grads

        # --- Average Training Metrics ---
        avg_train_loss = np.mean(epoch_train_losses) if epoch_train_losses else float('inf')
        for k in epoch_train_components:
            epoch_train_components[k] /= len(train_loader) if len(train_loader) > 0 else 1
            component_train_losses[k].append(epoch_train_components[k])
        train_losses.append(avg_train_loss)

        # --- Validation Phase ---
        model.eval()
        epoch_val_losses = []
        epoch_val_components = {k: 0.0 for k in component_val_losses}

        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f"Epoch {epoch+1}/{num_epochs} [Val]", leave=False)
            for images, targets in val_pbar:
                images = images.float().to(device, non_blocking=True)
                targets = targets.float().to(device, non_blocking=True)

                with autocast(device_type=device.type):
                    outputs = model(images)
                    loss, loss_dict = criterion(outputs, targets)

                if not torch.isnan(loss) and not torch.isinf(loss):
                    epoch_val_losses.append(loss.item())
                    for k in epoch_val_components:
                        if k in loss_dict:
                            epoch_val_components[k] += loss_dict[k].item()

                if len(epoch_val_losses) % 10 == 0 or len(epoch_val_losses) == len(val_loader):
                    val_pbar.set_postfix({'Loss': f'{loss.item():.4f}'})

        # --- Average Validation Metrics ---
        avg_val_loss = np.mean(epoch_val_losses) if epoch_val_losses else float('inf')
        for k in epoch_val_components:
            epoch_val_components[k] /= len(val_loader) if len(val_loader) > 0 else 1
            component_val_losses[k].append(epoch_val_components[k])
        val_losses.append(avg_val_loss)

        # --- Epoch Summary ---
        current_lr = optimizer.param_groups[0]['lr']
        print(f"Epoch {epoch+1} Summary:")
        print(f"  Train Loss: {avg_train_loss:.6f} | Val Loss: {avg_val_loss:.6f} | LR: {current_lr:.2e}")
        train_comp_str = " | ".join([f"{k}: {v[-1]:.4f}" for k, v in component_train_losses.items() if v])
        val_comp_str = " | ".join([f"{k}: {v[-1]:.4f}" for k, v in component_val_losses.items() if v])
        print(f"  Train Components: {train_comp_str}")
        print(f"  Val Components:   {val_comp_str}")

        # --- 10. Early Stopping & Best Model Saving ---
        if avg_val_loss < best_loss:
            best_loss = avg_val_loss
            epochs_since_improvement = 0
            best_checkpoint = {
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'epoch': epoch,
                'loss': best_loss,
                'epochs_since_improvement': epochs_since_improvement,
                'train_losses': train_losses,
                'val_losses': val_losses,
                'component_train_losses': component_train_losses,
                'component_val_losses': component_val_losses
            }
            torch.save(best_checkpoint, best_model_path)
            print(f"  ✅ New best model saved (Val Loss: {best_loss:.6f})")
        else:
            epochs_since_improvement += 1
            print(f"  ⏱️  Epochs since last improvement: {epochs_since_improvement}")

        if epochs_since_improvement >= early_stopping_patience:
            print(f"\n⚠️ Early stopping triggered after {epoch + 1} epochs (patience {early_stopping_patience}).")
            break

        # --- 11. Periodic Visualization ---
        if epoch % max(1, num_epochs // 20) == 0 or epoch == num_epochs - 1 or epoch == start_epoch:
            visualize_training_progress(
                model, val_loader, device, model_dir, epoch,
                train_losses, val_losses,
                component_train_losses, component_val_losses
            )

    # --- 12. Finalization ---
    print("\n--- Training Completed ---")
    final_checkpoint = {
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict(),
        'epoch': len(train_losses) - 1,
        'loss': val_losses[-1] if val_losses else float('inf'),
        'epochs_since_improvement': epochs_since_improvement,
        'train_losses': train_losses,
        'val_losses': val_losses,
        'component_train_losses': component_train_losses,
        'component_val_losses': component_val_losses
    }
    torch.save(final_checkpoint, final_model_path)
    print(f"💾 Final model checkpoint saved to {final_model_path}")

    # --- 13. Plot Final Loss Curves ---
    try:
        plt.figure(figsize=(12, 5))
        plt.subplot(1, 2, 1)
        epochs_plotted = range(1, len(train_losses) + 1)
        plt.plot(epochs_plotted, train_losses, label='Train Loss')
        plt.plot(epochs_plotted, val_losses, label='Val Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Overall Training & Validation Loss')
        plt.legend()
        plt.grid(True)

        plt.subplot(1, 2, 2)
        for k, v in component_train_losses.items():
            if v:
                plt.plot(epochs_plotted, v, label=f'Train {k}')
        for k, v in component_val_losses.items():
            if v:
                val_comp_aligned = v[:len(epochs_plotted)]
                plt.plot(epochs_plotted, val_comp_aligned, label=f'Val {k}', linestyle='--')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Component Losses')
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        loss_curve_path = os.path.join(model_dir, 'final_loss_curves.png')
        plt.savefig(loss_curve_path)
        plt.close()
        print(f"📊 Final loss curves plot saved to {loss_curve_path}")
    except Exception as e:
        print(f"⚠️ Error plotting final loss curves: {e}")

    print(f"\n🎉 Training finished. Best validation loss: {best_loss:.6f}")
    return model, train_losses, val_losses

print("✅ State-of-the-Art training function 'train_skeleton_aware_model_sota' with dynamic curriculum and visualization is ready for use.")


print(f"Fused optimizers available: {hasattr(torch.optim.AdamW, '__init__') and 'fused' in torch.optim.AdamW.__init__.__code__.co_varnames}")




# Set paths
image_dir = "/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/images/"
mask_dir = "/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/masks/"
image_paths = sorted(glob.glob(os.path.join(image_dir, '*.tif')))
mask_paths = sorted(glob.glob(os.path.join(mask_dir, '*.tif')))

# # Example usage (uncomment to run):
# device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
# model = SkeletonAwareSpotDetector(in_ch=1, base_ch=48).to(device)
# # # Load a trained model if available
# # # checkpoint = torch.load('path/to/best_model.pth')
# # # model.load_state_dict(checkpoint['model_state_dict'])
# # 
# # # Create data loaders (replace with your actual paths)
# train_loader, val_loader = create_skeleton_aware_data_loaders_fixed(image_paths, mask_paths)
# # 
# # # Run inspection# 
# results = inspect_skeleton_aware_model(model, val_loader, device, num_batches=2)




# Cell 9: Complete Pipeline with Updated Paths and SOTA Components

import os
import glob
import torch
import numpy as np
import tifffile
import matplotlib.pyplot as plt
from skimage.color import label2rgb

# --- 1. Set Paths ---
image_dir = "/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/images/"
mask_dir = "/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/masks/"
image_paths = sorted(glob.glob(os.path.join(image_dir, '*.tif')))
mask_paths = sorted(glob.glob(os.path.join(mask_dir, '*.tif')))

print(f"Found {len(image_paths)} images and {len(mask_paths)} masks")

# Verify a few paths
if image_paths and mask_paths:
    print("Sample image path:", image_paths[0])
    print("Sample mask path:", mask_paths[0])
    
    # Check if corresponding files exist
    base_name = os.path.splitext(os.path.basename(image_paths[0]))[0]
    expected_mask = os.path.join(mask_dir, base_name + '.tif')
    if os.path.exists(expected_mask):
        print("✓ Matching mask found for first image")
    else:
        print("⚠️  Matching mask not found for first image")

# --- 2. Configuration ---
model_dir = '/mnt/d/Users/<USER>/Documents/spot_detector_model_sota_08062025/' # Updated directory name
os.makedirs(model_dir, exist_ok=True)
print(f"Model will be saved to: {model_dir}")

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

# --- 3. Create Data Loaders (using the FIXED version) ---
print("Creating skeleton-aware data loaders...")
# Disable pin_memory to prevent CUDA OOM errors as identified previously
train_loader, val_loader = create_skeleton_aware_data_loaders_fixed(
    image_paths, mask_paths, 
    batch_size=12, 
    patch_size=256, 
    num_workers=10 # Reduced from 4 to potentially improve stability if stuttering was an issue
)

print(f"Training samples: {len(train_loader.dataset)}")
print(f"Validation samples: {len(val_loader.dataset)}")


# Run inspection on validation data
inspection_results = inspect_ground_truth_data(
    val_loader, 
    device, 
    num_batches=2, 
    save_dir="gt_inspection_output"
)

from scipy.ndimage import maximum_filter
from skimage.segmentation import watershed,find_boundaries
# --- 4. Initialize Model ---
print("Initializing skeleton-aware model...")
# Use the latest, corrected model definition
model = SkeletonAwareSpotDetector(in_ch=1, base_ch=48) 
print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")

# --- 5. TRAINING with SOTA Function ---
print("Starting SOTA skeleton-aware training (with resume and early stopping)...")
# Use the State-of-the-Art training function with early stopping
trained_model, train_losses, val_losses = train_skeleton_aware_model_sota(
    model, 
    train_loader, 
    val_loader, 
    num_epochs=300,
    device=device,
    model_dir=model_dir,
    resume=True, # Set to False if you want to force a fresh start
    early_stopping_patience=30 # Stop if no improvement for 30 epochs
)

# --- 6. Save Final Model State ---
# The SOTA function already saves 'best_model.pth' and 'final_model.pth'
# This additional save is redundant but kept for explicitness if needed for a specific purpose.
# It saves the model returned by the training function.
final_model_path_additional = os.path.join(model_dir, 'final_model_from_pipeline.pth')
torch.save({
    'model_state_dict': trained_model.state_dict(),
    'model_config': {
        'in_ch': 1,
        'base_ch': 48
    },
    'train_losses': train_losses,
    'val_losses': val_losses
}, final_model_path_additional)
print(f"Additional final model saved to: {final_model_path_additional}")

# --- 7. Evaluate Model ---
print("Running skeleton-aware evaluation...")


def evaluate_skeleton_aware_model(model, val_loader, device='cuda'):
    model.eval()
    all_metrics = {'precision': [], 'recall': [], 'f1': [], 'skeleton_iou': [], 'sdt_mse': [], 'boundary_iou': []}
    with torch.no_grad():
        for images, targets in val_loader:
            images = images.to(device, non_blocking=True)
            targets = targets.to(device, non_blocking=True)
            batch_size = images.shape[0]
            for i in range(batch_size):
                try:
                    with autocast('cuda'):
                        outputs = model(images[i:i+1])
                    # Process SDT
                    sdt_probs = F.softmax(outputs['sdt_out'], dim=1)
                    bin_centers = (torch.arange(0, sdt_probs.shape[1], device=sdt_probs.device, dtype=torch.float32) + 0.5) / sdt_probs.shape[1]
                    bin_centers = bin_centers.view(1, -1, 1, 1)
                    pred_sdt = torch.sum(sdt_probs * bin_centers, dim=1, keepdim=True)
                    # Predictions
                    pred_semantic = torch.sigmoid(outputs['sem_out']) > 0.5
                    pred_skeleton = torch.sigmoid(outputs['skeleton_out']) > 0.5
                    # Ground truth
                    gt_semantic = targets[i:i+1, 0:1] > 0.5
                    gt_skeleton = targets[i:i+1, 2:3] > 0.5
                    gt_boundary = targets[i:i+1, 6:7] > 0.5 # Corrected index
                    # Metrics
                    tp = (pred_semantic & gt_semantic).float().sum().item()
                    fp = (pred_semantic & ~gt_semantic).float().sum().item()
                    fn = (~pred_semantic & gt_semantic).float().sum().item()
                    precision = tp / (tp + fp + 1e-6)
                    recall = tp / (tp + fn + 1e-6)
                    f1 = 2 * (precision * recall) / (precision + recall + 1e-6)
                    skeleton_iou = (pred_skeleton & gt_skeleton).float().sum().item() / ((pred_skeleton | gt_skeleton).float().sum().item() + 1e-6)
                    boundary_iou = (pred_semantic & gt_boundary).float().sum().item() / ((pred_semantic | gt_boundary).float().sum().item() + 1e-6)
                    sdt_mse = F.mse_loss(pred_sdt, targets[i:i+1, 1:2]).item()
                    
                    all_metrics['precision'].append(precision)
                    all_metrics['recall'].append(recall)
                    all_metrics['f1'].append(f1)
                    all_metrics['skeleton_iou'].append(skeleton_iou)
                    all_metrics['sdt_mse'].append(sdt_mse)
                    all_metrics['boundary_iou'].append(boundary_iou)
                except Exception as e:
                    print(f"Evaluation error: {e}")
                    continue
    eval_metrics = {}
    for k, v in all_metrics.items():
        eval_metrics[k] = np.mean(v) if v else 0.0
        eval_metrics[f'std_{k}'] = np.std(v) if v else 0.0
    return eval_metrics


# Ensure evaluate_skeleton_aware_model is defined or imported
eval_metrics = evaluate_skeleton_aware_model(trained_model, val_loader, device)
print(f"Evaluation Results:")
for k, v in eval_metrics.items():
    if not k.startswith('std_'):
        std_key = f'std_{k}'
        std_val = eval_metrics.get(std_key, 'N/A')
        print(f"  {k.capitalize()}: {v:.4f} ± {std_val:.4f}")

# --- 8. Test Inference on Sample Images ---
print("Testing skeleton-aware inference...")
test_images = image_paths[:3]  # Test on first 3 images

# --- CRITICAL: Use the FIXED inference function ---
# This should be the `tile_inference` function that correctly handles SDT reconstruction
# and uses the improved instance segmentation.
# Assuming `tile_inference` is defined in the current notebook/script context.
# If it's in a separate file, import it: from your_inference_module import tile_inference

for i, test_path in enumerate(test_images):
    print(f"Testing on image {i+1}: {os.path.basename(test_path)}")
    
    # --- Use the robust tiled inference function ---
    # This function handles large images, SDT correctly, and instance segmentation
    try:
        inference_results = tile_inference(
            trained_model,
            test_path,
            device=device,
            patch_size=256,
            overlap=0.5, # Standard overlap
            batch_size=1 # Process one patch at a time for stability during testing
        )
        # Extract results
        # The keys depend on the exact output of your `tile_inference` function
        # Based on the provided `tile_inference` code:
        semantic_result = inference_results.get('semantic', np.zeros((10, 10)))
        sdt_result = inference_results.get('sdt_scalar', np.zeros((10, 10))) # Use scalar SDT
        skeleton_result = inference_results.get('skeleton', np.zeros((10, 10)))
        instance_labels_result = inference_results.get('instance_labels', np.zeros((10, 10), dtype=int))
        spots_result = inference_results.get('spots', [])
        
        # Load original image for visualization
        original_image = tifffile.imread(test_path).astype(np.float32)
        vmin, vmax = np.percentile(original_image, (1, 99))
        original_image_norm = np.clip((original_image - vmin) / (vmax - vmin + 1e-8), 0, 1)

    except Exception as e:
        print(f"  ❌ Error during inference for {os.path.basename(test_path)}: {e}")
        # Create dummy results to allow visualization code to run
        original_image_norm = np.zeros((256, 256))
        semantic_result = np.zeros((256, 256))
        sdt_result = np.zeros((256, 256))
        skeleton_result = np.zeros((256, 256))
        instance_labels_result = np.zeros((256, 256), dtype=int)
        spots_result = []

    # --- Visualize results ---
    try:
        plt.figure(figsize=(20, 15)) # Increased figure size for more plots
        
        # 1. Original image with detected spots
        plt.subplot(3, 3, 1)
        plt.imshow(original_image_norm, cmap='gray')
        if spots_result:
            # Handle both list of dicts and array formats
            if isinstance(spots_result, list) and len(spots_result) > 0 and isinstance(spots_result[0], dict):
                 spots_y = [spot['y'] for spot in spots_result]
                 spots_x = [spot['x'] for spot in spots_result]
            elif isinstance(spots_result, np.ndarray) and spots_result.size > 0:
                 spots_y = spots_result[:, 0]
                 spots_x = spots_result[:, 1]
            else:
                 spots_y, spots_x = [], []
            if spots_y:
                plt.scatter(spots_x, spots_y, c='red', s=30, marker='o', alpha=0.7)
        plt.title(f"Detected Spots: {len(spots_result)}")
        plt.axis('off')

        # 2. Semantic mask
        plt.subplot(3, 3, 2)
        plt.imshow(semantic_result, cmap='viridis')
        plt.title("Semantic Mask")
        plt.axis('off')
        plt.colorbar(shrink=0.8)

        # 3. SDT (Scalar)
        plt.subplot(3, 3, 3)
        plt.imshow(sdt_result, cmap='magma')
        plt.title("SDT (Scalar)")
        plt.axis('off')
        plt.colorbar(shrink=0.8)

        # 4. Skeleton
        plt.subplot(3, 3, 4)
        plt.imshow(skeleton_result, cmap='bone')
        plt.title("Skeleton")
        plt.axis('off')

        # 5. Instance segmentation
        plt.subplot(3, 3, 5)
        instance_viz = label2rgb(instance_labels_result, bg_label=0, alpha=0.7)
        plt.imshow(instance_viz)
        plt.title(f"Instance Segmentation ({np.max(instance_labels_result)} instances)")
        plt.axis('off')

        # 6. Centroid Map (if available in results)
        centroid_result = inference_results.get('centroid', np.zeros((10, 10)))
        plt.subplot(3, 3, 6)
        plt.imshow(centroid_result, cmap='hot')
        plt.title("Centroid Heatmap")
        plt.axis('off')
        plt.colorbar(shrink=0.8)
        
        # 7. Flow Magnitude (if available)
        flow_result = inference_results.get('flow', np.zeros((2, 10, 10)))
        if flow_result is not None and flow_result.ndim == 3 and flow_result.shape[0] == 2:
            flow_mag = np.sqrt(flow_result[0]**2 + flow_result[1]**2 + 1e-8)
            plt.subplot(3, 3, 7)
            plt.imshow(flow_mag, cmap='plasma')
            plt.title("Flow Magnitude")
            plt.axis('off')
            plt.colorbar(shrink=0.8)
        else:
             plt.subplot(3, 3, 7)
             plt.text(0.5, 0.5, 'Flow Not Available', ha='center', va='center')
             plt.title("Flow")
             plt.axis('off')

        # 8. Overlay of instances on image
        plt.subplot(3, 3, 8)
        plt.imshow(original_image_norm, cmap='gray')
        # Overlay instance boundaries
        if np.max(instance_labels_result) > 0:
            # Simple boundary visualization
            from skimage.segmentation import find_boundaries
            boundaries = find_boundaries(instance_labels_result, mode='outer')
            plt.imshow(boundaries, cmap='red', alpha=0.5)
        plt.title("Instances Overlay")
        plt.axis('off')
        
        # 9. Summary text or another relevant plot
        plt.subplot(3, 3, 9)
        plt.text(0.1, 0.8, f"Image: {os.path.basename(test_path)}", fontsize=10)
        plt.text(0.1, 0.6, f"Spots Detected: {len(spots_result)}", fontsize=10)
        plt.text(0.1, 0.4, f"Max Instance ID: {np.max(instance_labels_result)}", fontsize=10)
        # Add a simple histogram of SDT values if interesting
        if sdt_result is not None and sdt_result.size > 1:
            plt.text(0.1, 0.2, f"SDT Mean: {np.mean(sdt_result):.3f}", fontsize=10)
        plt.title("Summary")
        plt.axis('off')
        plt.gca().set_facecolor('lightgray') # Background for text

        plt.suptitle(f"Inference Results for {os.path.basename(test_path)}", fontsize=16)
        plt.tight_layout()
        result_plot_path = os.path.join(model_dir, f'inference_result_{i+1}_{os.path.splitext(os.path.basename(test_path))[0]}.png')
        plt.savefig(result_plot_path, dpi=150, bbox_inches='tight')
        plt.close()
        print(f"  📊 Inference results plot saved to: {result_plot_path}")
        print(f"  🧮 Detected {len(spots_result)} spots/instances.")
        
    except Exception as e:
        print(f"  ❌ Error during visualization for {os.path.basename(test_path)}: {e}")
        # Try to close any open figures to prevent resource leaks
        plt.close('all') 

# --- 9. Memory cleanup ---
torch.cuda.empty_cache()
print("\n🎉 Training and inference pipeline completed successfully!")
print(f"🏁 All outputs and models are saved in: {model_dir}")



# Cell: Model Refinement and Further Improvement

import torch
import torch.nn as nn
import numpy as np
import os
import matplotlib.pyplot as plt
from sklearn.metrics import precision_recall_curve, average_precision_score

def refine_trained_model(
    initial_model_path, 
    image_paths, 
    mask_paths,
    refinement_config,
    device='cuda'
):
    """
    Refine a trained model based on initial results.

    Args:
        initial_model_path (str): Path to the 'best_model.pth' or 'final_model.pth'.
        image_paths (list): List of image file paths.
        mask_paths (list): List of mask file paths.
        refinement_config (dict): Configuration for refinement.
            - 'strategy': 'fine_tune' or 'evaluate_only'
            - 'new_lr': New learning rate for fine-tuning.
            - 'new_epochs': Number of epochs for fine-tuning.
            - 'batch_size': Batch size for refinement data loaders.
            - 'patch_size': Patch size for refinement data loaders.
            - 'num_workers': Number of workers for data loaders.
            - 'loss_weights': Optional dict to adjust loss function weights.
        device (str): Device to run on ('cuda' or 'cpu').

    Returns:
        dict: Results of refinement including model path and metrics.
    """
    print("--- Model Refinement Process ---")
    print(f"Loading initial model from: {initial_model_path}")
    
    # --- 1. Load Initial Model ---
    if not os.path.exists(initial_model_path):
        raise FileNotFoundError(f"Initial model path not found: {initial_model_path}")
        
    checkpoint = torch.load(initial_model_path, map_location=device, weights_only=False)
    
    # Determine model configuration
    # Assuming the model config is saved, otherwise use defaults
    model_config = checkpoint.get('model_config', {'in_ch': 1, 'base_ch': 48})
    print(f"Model config: {model_config}")
    
    # Initialize model
    refined_model = SkeletonAwareSpotDetector(**model_config).to(device)
    refined_model.load_state_dict(checkpoint['model_state_dict'])
    print("✅ Initial model loaded successfully.")

    # --- 2. Prepare Data ---
    print("\n--- Preparing Data for Refinement ---")
    # Use the fixed data loader function
    refinement_train_loader, refinement_val_loader = create_skeleton_aware_data_loaders_fixed(
        image_paths, mask_paths,
        batch_size=refinement_config.get('batch_size', 12),
        patch_size=refinement_config.get('patch_size', 256),
        num_workers=refinement_config.get('num_workers', 4)
    )
    print(f"Refinement DataLoader - Train: {len(refinement_train_loader.dataset)}, Val: {len(refinement_val_loader.dataset)}")

    # --- 3. Refinement Strategy ---
    strategy = refinement_config.get('strategy', 'evaluate_only')
    
    if strategy == 'evaluate_only':
        print("\n--- Evaluation Only Strategy ---")
        refined_model.eval()
        print("Running detailed evaluation on validation set...")
        final_metrics = evaluate_skeleton_aware_model(refined_model, refinement_val_loader, device)
        print("Final Refined Model Evaluation Metrics:")
        for k, v in final_metrics.items():
            if not k.startswith('std_'):
                std_key = f'std_{k}'
                std_val = final_metrics.get(std_key, 'N/A')
                print(f"  {k.capitalize()}: {v:.4f} ± {std_val:.4f}")
        
        # Run inspection on the refined model
        print("\nRunning model inspection on refined model...")
        inspection_results = inspect_skeleton_aware_model(
            refined_model, refinement_val_loader, device, num_batches=2,
            save_dir=os.path.join(model_dir, "refinement_inspection")
        )
        
        return {
            'status': 'evaluation_completed',
            'model_path': initial_model_path,
            'metrics': final_metrics,
            'inspection_results': inspection_results
        }

    elif strategy == 'fine_tune':
        print("\n--- Fine-Tuning Strategy ---")
        
        # --- 4. Setup Fine-Tuning ---
        # Create a new model directory for refined model
        refined_model_dir = os.path.join(model_dir, "refined_model")
        os.makedirs(refined_model_dir, exist_ok=True)
        print(f"Refined model will be saved to: {refined_model_dir}")
        
        # Adjust loss function if specified
        if 'loss_weights' in refinement_config:
            print("Adjusting loss function weights for fine-tuning...")
            # This would require modifying the loss function instance
            # For simplicity here, we assume the main loss function is used
            # A more robust approach would be to create a new loss instance
            # with the specified weights.
            print("Note: Loss weight adjustment not implemented in this snippet. Modify SkeletonAwareLoss directly if needed.")
        
        # Freeze certain layers if specified (example)
        if refinement_config.get('freeze_encoder', False):
            print("Freezing encoder layers...")
            for name, param in refined_model.named_parameters():
                if name.startswith(('stem_conv', 'enc1', 'enc2')):
                    param.requires_grad = False
            print("Encoder layers frozen.")
        
        # Setup optimizer and scheduler for fine-tuning
        new_lr = refinement_config.get('new_lr', 1e-4) # Lower LR for fine-tuning
        new_epochs = refinement_config.get('new_epochs', 50)
        print(f"Fine-tuning with LR: {new_lr}, Epochs: {new_epochs}")
        
        optimizer = torch.optim.AdamW(
            filter(lambda p: p.requires_grad, refined_model.parameters()), # Only optimize unfrozen params
            lr=new_lr,
            weight_decay=1e-4,
            betas=(0.9, 0.999)
        )
        scheduler = torch.optim.lr_scheduler.OneCycleLR(
            optimizer,
            max_lr=new_lr,
            epochs=new_epochs,
            steps_per_epoch=len(refinement_train_loader),
            pct_start=0.1,
            anneal_strategy='cos'
        )
        
        # Use the main loss function
        criterion = SkeletonAwareLoss() # Re-instantiate or use existing if accessible
        
        # --- 5. Fine-Tuning Loop (Simplified version of the main training loop) ---
        print("Starting fine-tuning...")
        best_loss = float('inf')
        refined_train_losses, refined_val_losses = [], []
        
        scaler = GradScaler() # Re-use AMP scaler
        
        for epoch in range(new_epochs):
            # Training
            refined_model.train()
            epoch_train_losses = []
            pbar = tqdm(refinement_train_loader, desc=f"FT Epoch {epoch+1}/{new_epochs}")
            for images, targets in pbar:
                images = images.float().to(device, non_blocking=True)
                targets = targets.float().to(device, non_blocking=True)
                optimizer.zero_grad(set_to_none=True)
                with autocast('cuda'):
                    outputs = refined_model(images)
                    loss, _ = criterion(outputs, targets)
                if not torch.isnan(loss):
                    scaler.scale(loss).backward()
                    scaler.unscale_(optimizer)
                    torch.nn.utils.clip_grad_norm_(refined_model.parameters(), max_norm=1.0)
                    scaler.step(optimizer)
                    scaler.update()
                    scheduler.step()
                    epoch_train_losses.append(loss.item())
                pbar.set_postfix({'Loss': f'{loss.item():.4f}' if not torch.isnan(loss) else 'NaN'})
            
            train_loss = np.mean(epoch_train_losses) if epoch_train_losses else float('inf')
            refined_train_losses.append(train_loss)
            
            # Validation
            refined_model.eval()
            epoch_val_losses = []
            with torch.no_grad():
                for images, targets in refinement_val_loader:
                    images = images.float().to(device, non_blocking=True)
                    targets = targets.float().to(device, non_blocking=True)
                    with autocast('cuda'):
                        outputs = refined_model(images)
                        loss, _ = criterion(outputs, targets)
                    if not torch.isnan(loss):
                        epoch_val_losses.append(loss.item())
            
            val_loss = np.mean(epoch_val_losses) if epoch_val_losses else float('inf')
            refined_val_losses.append(val_loss)
            
            print(f"FT Epoch {epoch+1}: Train Loss: {train_loss:.4f} | Val Loss: {val_loss:.4f}")
            print(f"  LR: {optimizer.param_groups[0]['lr']:.6e}")
            
            # Save best refined model
            if val_loss < best_loss:
                best_loss = val_loss
                refined_checkpoint_path = os.path.join(refined_model_dir, 'best_refined_model.pth')
                torch.save({
                    'model_state_dict': refined_model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'scheduler_state_dict': scheduler.state_dict(),
                    'epoch': epoch,
                    'loss': best_loss,
                    'model_config': model_config,
                    'refinement_config': refinement_config
                }, refined_checkpoint_path)
                print(f"✅ Best refined model saved (val_loss={best_loss:.4f})")
        
        # Plot refinement loss curves
        plt.figure(figsize=(10, 5))
        plt.plot(refined_train_losses, label='Refined Train Loss')
        plt.plot(refined_val_losses, label='Refined Val Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Refinement Loss Curves')
        plt.legend()
        plt.grid(True)
        plt.savefig(os.path.join(refined_model_dir, 'refinement_loss_curves.png'))
        plt.close()
        print("Refinement loss curves saved.")
        
        # --- 6. Final Evaluation ---
        print("\nRunning final evaluation on refined model...")
        final_metrics = evaluate_skeleton_aware_model(refined_model, refinement_val_loader, device)
        print("Final Refined Model Evaluation Metrics:")
        for k, v in final_metrics.items():
            if not k.startswith('std_'):
                std_key = f'std_{k}'
                std_val = final_metrics.get(std_key, 'N/A')
                print(f"  {k.capitalize()}: {v:.4f} ± {std_val:.4f}")
        
        # Run inspection on the refined model
        print("\nRunning model inspection on refined model...")
        inspection_results = inspect_skeleton_aware_model(
            refined_model, refinement_val_loader, device, num_batches=2,
            save_dir=os.path.join(refined_model_dir, "refinement_inspection")
        )
        
        return {
            'status': 'fine_tuning_completed',
            'model_path': refined_checkpoint_path,
            'metrics': final_metrics,
            'inspection_results': inspection_results,
            'train_losses': refined_train_losses,
            'val_losses': refined_val_losses
        }
        
    else:
        raise ValueError(f"Unknown refinement strategy: {strategy}")


# --- Example Usage ---

# 1. Specify paths (assuming these are already defined from your main script)
# image_dir = "/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/images/"
# mask_dir = "/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/masks/"
# image_paths = sorted(glob.glob(os.path.join(image_dir, '*.tif')))
# mask_paths = sorted(glob.glob(os.path.join(mask_dir, '*.tif')))
# model_dir = '/mnt/d/Users/<USER>/Documents/spot_detector_model/' # Main model directory

# 2. Determine the path to the best model from initial training
# This could be the 'best_model.pth' saved during training or 'final_model.pth'
initial_model_path = os.path.join(model_dir, 'best_model.pth') # Or 'final_model.pth'

# 3. Define refinement configurations

# Option A: Evaluate Only (No further training)
refinement_config_eval = {
    'strategy': 'evaluate_only'
}

# Option B: Fine-Tune with lower LR and fewer epochs
refinement_config_finetune = {
    'strategy': 'fine_tune',
    'new_lr': 5e-5,          # Lower learning rate for fine-tuning
    'new_epochs': 30,        # Fewer epochs for refinement
    'batch_size': 12,        # Can adjust if needed
    'patch_size': 256,
    'num_workers': 2,        # Reduce workers if you had stuttering issues
    # 'loss_weights': {'w_sem': 1.2, 'w_sdt': 2.5} # Example of adjusting weights
    # 'freeze_encoder': True   # Example of freezing encoder layers
}

# 4. Choose a configuration and run refinement
# Uncomment one of the lines below to choose the strategy

# For Evaluation Only:
# refinement_results = refine_trained_model(
#     initial_model_path, image_paths, mask_paths, refinement_config_eval, device
# )

# For Fine-Tuning:
# refinement_results = refine_trained_model(
#     initial_model_path, image_paths, mask_paths, refinement_config_finetune, device
# )

# 5. Access results
# print("\n--- Refinement Results ---")
# print(f"Status: {refinement_results['status']}")
# print(f"Model Path: {refinement_results['model_path']}")
# if 'metrics' in refinement_results:
#     print("Final Metrics:")
#     for k, v in refinement_results['metrics'].items():
#         if not k.startswith('std_'):
#             std_key = f'std_{k}'
#             std_val = refinement_results['metrics'].get(std_key, 'N/A')
#             print(f"  {k.capitalize()}: {v:.4f} ± {std_val:.4f}")

print("Refinement code is ready. Uncomment the example usage section to run.")


Explanation:
refine_trained_model Function:
Takes the path to your initially trained model, data paths, a configuration dictionary for refinement, and the device.
Loads the Model: It loads the state dictionary from the checkpoint file. It tries to get the model configuration from the checkpoint; if not found, it uses defaults. It then initializes a new SkeletonAwareSpotDetector instance and loads the saved weights.
Prepares Data: It uses your create_skeleton_aware_data_loaders_fixed function to create new data loaders for the refinement process. This ensures consistency and applies the fixes (like pin_memory=False).
Strategy Selection:
evaluate_only: If you just want to get a detailed report or run the inspection on the current best model without changing it, this option loads the model, runs evaluate_skeleton_aware_model, and inspect_skeleton_aware_model. It returns the results.
fine_tune: This is for further training. It sets up a new directory for the refined model.
Fine-Tuning Setup (fine_tune strategy):
Creates a new directory refined_model within your main model_dir.
Allows for potential adjustments like changing the learning rate (new_lr), number of epochs (new_epochs), or batch size/worker settings.
Includes an example of how you might freeze encoder layers (useful if you think the encoder is good but heads need more work).
Sets up a new AdamW optimizer, typically with a lower learning rate than the initial training. It uses filter(lambda p: p.requires_grad, ...) to ensure only unfrozen parameters are optimized.
Sets up a new OneCycleLR scheduler tailored for the fine-tuning run.
Fine-Tuning Loop (fine_tune strategy):
Runs a simplified version of the main training loop using the new optimizer and scheduler.
Saves the best model based on validation loss during this refinement phase to best_refined_model.pth.
Plots and saves the loss curves for the refinement phase.
Final Evaluation: Regardless of the strategy, it performs a final evaluate_skeleton_aware_model and inspect_skeleton_aware_model on the resulting model (either the original loaded one or the newly fine-tuned one).
Returns: A dictionary summarizing the outcome, including the path to the final model file, metrics, and inspection results.
Example Usage:
Shows how to define paths and two example refinement_config dictionaries.
Demonstrates how to call the refine_trained_model function with either configuration.
Shows how to access the results returned by the function.
This code provides a structured way to take your trained model and either get a final, detailed analysis or perform targeted fine-tuning to potentially squeeze out a bit more performance. You can easily modify the refinement_config to experiment with different fine-tuning approaches (e.g., different LRs, freezing different parts of the network, adjusting epochs).

# Cell: Optimized Tiled Inference & Instance Segmentation
import torch
import torch.nn.functional as F
import numpy as np
from tqdm import tqdm
import tifffile
from scipy.ndimage import maximum_filter
from skimage.segmentation import watershed, find_boundaries
from skimage.measure import regionprops
import cv2
import os

try:
    from torch.amp import autocast  # PyTorch 2.0+
except ImportError:
    from torch.cuda.amp import autocast  # PyTorch < 2.0


def skeleton_aware_instance_segmentation(
    semantic,
    sdt_scalar,
    skeleton,
    centroid_map,
    flow,
    min_size=5,
    nms_threshold=0.4,
    flow_refine_factor=0.3
):
    """
    Fast and accurate instance segmentation using all model outputs.
    Returns instance labels and a list of detected spots with refined centroids.
    """
    H, W = semantic.shape
    binary_mask = (semantic > 0.3).astype(np.uint8)

    # 1. Seed detection from centroid map
    peaks = (centroid_map > nms_threshold) & (maximum_filter(centroid_map, size=3) == centroid_map)
    centroid_coords = np.column_stack(np.where(peaks))

    if len(centroid_coords) == 0:
        print("No peaks detected above threshold.")
        return np.zeros((H, W), dtype=np.int32), []

    # 2. Create seeds for watershed
    seeds = np.zeros((H, W), dtype=np.int32)
    for idx, (y, x) in enumerate(centroid_coords, 1):
        seeds[y, x] = idx

    # 3. Watershed using negative SDT
    instance_labels = watershed(-sdt_scalar, seeds, mask=binary_mask)

    # 4. Relabel: one ID per connected component
    final_labels = np.zeros_like(instance_labels)
    regions = regionprops(instance_labels)
    new_id = 1
    filtered_spots = []

    for region in regions:
        if region.area < min_size:
            continue
        mask = (instance_labels == region.label)
        final_labels[mask] = new_id

        # 5. Refine centroid using flow
        cy, cx = region.centroid
        refined_cy, refined_cx = cy, cx
        if flow is not None and flow.shape[0] == 2:
            ys, xs = np.where(mask)
            if len(ys) > 0:
                avg_flow_y = flow[0][ys, xs].mean()
                avg_flow_x = flow[1][ys, xs].mean()
                refined_cy += flow_refine_factor * avg_flow_y
                refined_cx += flow_refine_factor * avg_flow_x

        # 6. Compute scores
        sdt_score = np.mean(sdt_scalar[mask])
        centroid_score = np.mean(centroid_map[mask])
        semantic_score = np.mean(semantic[mask])
        combined_score = (sdt_score + centroid_score + semantic_score) / 3.0

        filtered_spots.append({
            'y': float(refined_cy),
            'x': float(refined_cx),
            'original_y': float(cy),
            'original_x': float(cx),
            'score': float(combined_score),
            'size': int(region.area),
            'sdt_score': float(sdt_score),
            'centroid_score': float(centroid_score),
            'semantic_score': float(semantic_score)
        })
        new_id += 1

    print(f"Instance segmentation completed: Found {len(filtered_spots)} spots.")
    return final_labels, filtered_spots


def tile_inference(
    model,
    image_path,
    device='cuda',
    patch_size=256,
    overlap=0.5,
    batch_size=4,
    return_all_outputs=True
):
    """
    Fast tiled inference with full model output extraction and instance segmentation.
    Returns all outputs and final instance labels.
    """
    # 1. Load and normalize image
    image = tifffile.imread(image_path).astype(np.float32)
    H, W = image.shape
    vmin, vmax = np.percentile(image, (0.5, 99.5))
    image_norm = np.clip((image - vmin) / (vmax - vmin + 1e-8), 0, 1).astype(np.float32)

    # 2. Set model to eval mode
    model.eval()
    model.to(device)

    # 3. Calculate patch stride
    stride = int(patch_size * (1 - overlap))
    if stride <= 0:
        stride = patch_size // 2

    # 4. Initialize output accumulators
    def init_accumulator():
        return np.zeros((H, W), dtype=np.float32)

    semantic_out = init_accumulator()
    skeleton_out = init_accumulator()
    centroid_out = init_accumulator()
    sdt_probs_out = np.zeros((11, H, W), dtype=np.float32)  # 11 bins
    flow_out = np.zeros((2, H, W), dtype=np.float32)
    count_map = init_accumulator()

    # 5. Prepare patch indices
    y_coords = list(range(0, H, stride))
    x_coords = list(range(0, W, stride))
    total_patches = len(y_coords) * len(x_coords)

    # 6. Batched inference
    with torch.no_grad():
        patch_batch = []
        coords_batch = []
        pbar = tqdm(total=total_patches, desc="Tiled Inference")

        for y_start in y_coords:
            for x_start in x_coords:
                y_end = min(y_start + patch_size, H)
                x_end = min(x_start + patch_size, W)

                # Pad if needed
                pad_h = patch_size - (y_end - y_start)
                pad_w = patch_size - (x_end - x_start)
                patch = image_norm[y_start:y_end, x_start:x_end]
                patch = np.pad(patch, ((0, pad_h), (0, pad_w)), mode='reflect')

                patch_batch.append(patch)
                coords_batch.append((y_start, y_end, x_start, x_end))

                if len(patch_batch) >= batch_size or (y_start == y_coords[-1] and x_start == x_coords[-1]):
                    # Batch inference
                    patch_tensor = torch.from_numpy(np.stack(patch_batch)[..., None]).permute(0, 3, 1, 2).float().to(device)
                    with autocast('cuda'):
                        outputs = model(patch_tensor)

                    # Extract and stitch
                    for i, (y_start, y_end, x_start, x_end) in enumerate(coords_batch):
                        # Remove padding
                        h, w = y_end - y_start, x_end - x_start

                        if return_all_outputs:
                            semantic_out[y_start:y_end, x_start:x_end] += torch.sigmoid(outputs['sem_out'][i, 0]).cpu().numpy()[:h, :w]
                            skeleton_out[y_start:y_end, x_start:x_end] += torch.sigmoid(outputs['skeleton_out'][i, 0]).cpu().numpy()[:h, :w]
                            centroid_out[y_start:y_end, x_start:x_end] += torch.sigmoid(outputs['hm_out'][i, 0]).cpu().numpy()[:h, :w]
                            sdt_probs_out[:, y_start:y_end, x_start:x_end] += F.softmax(outputs['sdt_out'][i], dim=0).cpu().numpy()[:, :h, :w]
                            flow_out[:, y_start:y_end, x_start:x_end] += outputs['flow_out'][i].cpu().numpy()[:, :h, :w]

                        count_map[y_start:y_end, x_start:x_end] += 1
                    patch_batch = []
                    coords_batch = []
                    pbar.update(len(coords_batch) if not coords_batch else len(coords_batch))

        pbar.close()

    # 7. Normalize by count
    eps = 1e-8
    semantic_out /= (count_map + eps)
    skeleton_out /= (count_map + eps)
    centroid_out /= (count_map + eps)
    sdt_probs_out /= (count_map[None, ...] + eps)
    flow_out /= (count_map[None, ...] + eps)

    # 8. Reconstruct scalar SDT
    bin_centers = (np.arange(11) + 0.5) / 11
    sdt_scalar_out = np.sum(sdt_probs_out * bin_centers[:, None, None], axis=0)

    # 9. Instance Segmentation
    print("Performing instance segmentation...")
    instance_labels, filtered_spots = skeleton_aware_instance_segmentation(
        semantic=semantic_out,
        sdt_scalar=sdt_scalar_out,
        skeleton=skeleton_out,
        centroid_map=centroid_out,
        flow=flow_out,
        min_size=5,
        nms_threshold=0.4,
        flow_refine_factor=0.3
    )

    # 10. Prepare results
    results = {
        'instance_labels': instance_labels,
        'spots': filtered_spots,
        'semantic': semantic_out,
        'sdt_scalar': sdt_scalar_out,
        'skeleton': skeleton_out,
        'centroid': centroid_out,
        'flow': flow_out,
        'sdt_probs': sdt_probs_out if return_all_outputs else None
    }

    print(f"✅ Inference completed for {os.path.basename(image_path)}. Found {len(filtered_spots)} spots.")
    return results

# --- Example Usage: Load Model & Run Inference ---
import os
from pathlib import Path

# 1. Configuration
model_path = '/mnt/d/Users/<USER>/Documents/spot_detector_model_sota_centroid/best_model.pth'
image_path = '/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/images/0  Series001  Green--FLUO--FITC_tile_1.tif'
save_dir = Path(image_path).parent.parent / "inference_results"
save_dir.mkdir(exist_ok=True)

device = 'cuda' if torch.cuda.is_available() else 'cpu'
print(f"Using device: {device}")

# 2. Load Trained Model
print(f"Loading model from: {model_path}")
checkpoint = torch.load(model_path, map_location=device, weights_only=False)
model = SkeletonAwareSpotDetector(in_ch=1, base_ch=48)
model.load_state_dict(checkpoint['model_state_dict'])
model.to(device)
model.eval()

# 3. Run Fast Tiled Inference
print(f"Running inference on: {Path(image_path).name}")
results = tile_inference(
    model=model,
    image_path=image_path,
    device=device,
    patch_size=256,
    overlap=0.5,
    batch_size=8,  # Maximize GPU utilization
    return_all_outputs=True
)

# 4. Print Results
print(f"✅ Inference completed!")
print(f"   Found {len(results['spots'])} spots.")
print(f"   Output keys: {list(results.keys())}")
print(f"   Instance labels shape: {results['instance_labels'].shape}")
print(f"   Semantic mask range: [{results['semantic'].min():.3f}, {results['semantic'].max():.3f}]")

# 5. Save Results (Optional)
base_name = Path(image_path).stem
tifffile.imwrite(save_dir / f"{base_name}_instance_labels.tif", results['instance_labels'].astype(np.uint16))
np.save(save_dir / f"{base_name}_spots.npy", results['spots'])  # Save spot list
print(f"💾 Results saved to: {save_dir}")

# # Cell 9: Complete Pipeline with Updated Paths
# import os
# import glob

# # Set paths
# image_dir = "/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/images/"
# mask_dir = "/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/masks/"
# image_paths = sorted(glob.glob(os.path.join(image_dir, '*.tif')))
# mask_paths = sorted(glob.glob(os.path.join(mask_dir, '*.tif')))

# print(f"Found {len(image_paths)} images and {len(mask_paths)} masks")

# # Verify a few paths
# if image_paths and mask_paths:
#     print("Sample image path:", image_paths[0])
#     print("Sample mask path:", mask_paths[0])
    
#     # Check if corresponding files exist
#     base_name = os.path.splitext(os.path.basename(image_paths[0]))[0]
#     expected_mask = os.path.join(mask_dir, base_name + '.tif')
#     if os.path.exists(expected_mask):
#         print("✓ Matching mask found for first image")
#     else:
#         print("⚠️  Matching mask not found for first image")
        
# # Model output directory
# model_dir = '/mnt/d/Users/<USER>/Documents/spot_detector_model/'
# os.makedirs(model_dir, exist_ok=True)
# print(f"Model will be saved to: {model_dir}")

# # Set device
# device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
# print(f"Using device: {device}")

# # Create data loaders
# print("Creating skeleton-aware data loaders...")
# train_loader, val_loader = create_skeleton_aware_data_loaders(
#     image_paths, mask_paths, 
#     batch_size=12,  # Increased batch size for speed
#     patch_size=256, 
#     num_workers=4  # Reduced workers to avoid overhead
# )
# print(f"Training samples: {len(train_loader.dataset)}")
# print(f"Validation samples: {len(val_loader.dataset)}")

# # Initialize model
# print("Initializing skeleton-aware model...")
# model = SkeletonAwareSpotDetector(in_ch=1, base_ch=48)
# print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")

# # Train the model
# print("Starting skeleton-aware training...")
# trained_model, train_losses, val_losses = train_skeleton_aware_model_optimized(
#     model, 
#     train_loader, 
#     val_loader, 
#     num_epochs=300,  # Reduced epochs due to faster convergence
#     device=device,
#     model_dir=model_dir
# )

# # Save final model
# final_model_path = os.path.join(model_dir, 'final_model.pth')
# torch.save({
#     'model_state_dict': trained_model.state_dict(),
#     'model_config': {
#         'in_ch': 1,
#         'base_ch': 48
#     },
#     'train_losses': train_losses,
#     'val_losses': val_losses
# }, final_model_path)
# print(f"Final model saved to: {final_model_path}")

# # Evaluate model
# print("Running skeleton-aware evaluation...")
# eval_metrics = evaluate_skeleton_aware_model(trained_model, val_loader, device)
# print(f"Evaluation Results:")
# print(f"  Precision: {eval_metrics['precision']:.3f} ± {eval_metrics['std_precision']:.3f}")
# print(f"  Recall: {eval_metrics['recall']:.3f} ± {eval_metrics['std_recall']:.3f}")
# print(f"  F1-Score: {eval_metrics['f1']:.3f} ± {eval_metrics['std_f1']:.3f}")
# print(f"  Skeleton IoU: {eval_metrics['skeleton_iou']:.3f} ± {eval_metrics['std_skeleton_iou']:.3f}")
# print(f"  SDT MSE: {eval_metrics['sdt_mse']:.5f} ± {eval_metrics['std_sdt_mse']:.5f}")

# # Test inference on sample images
# print("Testing skeleton-aware inference...")
# test_images = image_paths[:3]  # Test on first 3 images
# for i, test_path in enumerate(test_images):
#     print(f"Testing on image {i+1}: {os.path.basename(test_path)}")
#     # Load test image
#     test_image = tifffile.imread(test_path).astype(np.float32)
#     test_image = test_image / (test_image.max() + 1e-8)
#     # Run inference
#     result = skeleton_aware_inference(
#         trained_model, 
#         test_image, 
#         device=device,
#         threshold=0.3,
#         min_distance=3,
#         nms_threshold=0.2
#     )
#     # Visualize results
#     plt.figure(figsize=(20, 10))
#     # Original image with spots
#     plt.subplot(2, 3, 1)
#     plt.imshow(test_image, cmap='gray')
#     if len(result['spots']) > 0:
#         spots_arr = np.array([[spot['y'], spot['x']] for spot in result['spots']])
#         plt.scatter(spots_arr[:, 1], spots_arr[:, 0], c='red', s=30, marker='o')
#     plt.title(f"Detected Spots: {len(result['spots'])}")
#     plt.axis('off')
#     # Semantic mask
#     plt.subplot(2, 3, 2)
#     plt.imshow(result['semantic'], cmap='viridis')
#     plt.title("Semantic Mask")
#     plt.axis('off')
#     # SDT
#     plt.subplot(2, 3, 3)
#     plt.imshow(result['sdt'], cmap='magma')
#     plt.title("Skeleton-Aware Distance Transform")
#     plt.axis('off')
#     # Skeleton
#     plt.subplot(2, 3, 4)
#     plt.imshow(result['skeleton'], cmap='bone')
#     plt.title("Skeleton")
#     plt.axis('off')
#     # Instance segmentation
#     instance_viz = label2rgb(result['instance_labels'], bg_label=0, alpha=0.7)
#     plt.subplot(2, 3, 5)
#     plt.imshow(instance_viz)
#     plt.title("Instance Segmentation")
#     plt.axis('off')
#     # Save visualization
#     plt.tight_layout()
#     plt.savefig(os.path.join(model_dir, f'test_result_{i+1}.png'), dpi=150)
#     plt.close()
#     print(f"  Detected {len(result['spots'])} spots")

# # Memory cleanup
# torch.cuda.empty_cache()
# print("Training completed successfully!")